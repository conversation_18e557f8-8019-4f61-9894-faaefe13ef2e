import React, { useEffect, useRef, useState } from 'react'
import styles from '../../../buyer/CreatePo/CreatePo.module.scss';
import clsx from 'clsx';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import { disputeCounterStatus, disputeStatusDisplayText, LargeProductsNameList, undo } from 'src/renderer2/common';
import { commomKeys, dateTimeFormat, format4DigitAmount, formatToTwoDecimalPlaces, getFloatRemainder, getValUsingUnitKey, orderIncrementPrefix, priceUnits, useBuyerSettingStore, useGlobalStore, useOrderManagementStore, userRole } from '@bryzos/giss-ui-library';
import { ReactComponent as DropDownArrowIcon } from '../../../../assets/New-images/StateIconDropDpown.svg';
import { ReactComponent as IconResolve } from '../../../../assets/New-images/New-Image-latest/Order-Management/icon-check1.svg';
import { ReactComponent as IconPending } from '../../../../assets/New-images/New-Image-latest/Order-Management/icon-check2.svg';
import { ReactComponent as IconReject } from '../../../../assets/New-images/New-Image-latest/Order-Management/icon-check3.svg';
import { Fade, MenuItem, Select, SelectChangeEvent, Tooltip, Popover } from '@mui/material';
import dayjs from 'dayjs';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { ReactComponent as PointRightIcon } from '../../../../assets/New-images/PointRight.svg';
import usePostDisputeOrder from 'src/renderer2/hooks/usePostDisputeOrder';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import usePostGetSellerPrices from 'src/renderer2/hooks/usePostGetSellerPrices';
import { ReactComponent as UndoIcon } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/icon-reset1.svg';

const DisputeTile = ({
    index,
    actualIndex,
    register,
    watch,
    setValue,
    currentFocusedItem,
    setFinalDisputePayload,
    finalDisputePayload,
    removeFromAttentionItems,
    isStateZipValChange,
    setCreatePoResultCopy,
    addToAttentionItems

}: {
    index: number;
    actualIndex: number;
    register: any;
    watch: any;
    setValue: any;
    currentFocusedItem: any;
    setFinalDisputePayload: any;
    finalDisputePayload: any;
    removeFromAttentionItems: any;
    isStateZipValChange: any;
    setCreatePoResultCopy: any;
    addToAttentionItems: any;
}) => {
    const [isAcceptDropdownOpen, setIsAcceptDropdownOpen] = useState(false);
    const [isCancelDropdownOpen, setIsCancelDropdownOpen] = useState(false);
    const [isCounter, setIsCounter] = useState(false);
    const [counterQty, setCounterQty] = useState("");
    const [counterQtyUnit, setCounterQtyUnit] = useState("");
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const [isAcceptCancelLineModalOpen, setIsAcceptCancelLineModalOpen] = useState(false);
    const [restockingFee, setRestockingFee] = useState("");
    const [counterQtyError, setCounterQtyError] = useState(false);
    const { showCommonDialog, resetDialogStore }: any = useDialogStore();
    const userData = useGlobalStore((state: any) => state.userData);
    const deliveryAllowedDates = useBuyerSettingStore(state => state.deliveryAllowedDates);
    const isEditingPo = useOrderManagementStore(state => state.isEditingPo);
    const orderManageMentInitialData = useOrderManagementStore(state => state.orderManageMentInitialData);
    const [cancelDropdownAnchorEl, setCancelDropdownAnchorEl] = useState<HTMLElement | null>(null);
    const calendarAnchorRef = useRef<HTMLSpanElement>(null);
    const [counterDeliveryDate, setCounterDeliveryDate] = useState("");

    const { mutateAsync: disputeOrderMutation } = usePostDisputeOrder();
    
    const isBuyer = userData?.data?.type === userRole.buyerUser;
    const isSupplier = userData?.data?.type === userRole.sellerUser;
    const lastCounterData = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1] : {};
    const isCounterResolved = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.counter_status === disputeCounterStatus.resolved;
    const isCounterPending = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && (lastCounterData?.counter_status === disputeCounterStatus.pending || lastCounterData?.counter_status === disputeCounterStatus.cancelled);
    const isCounterRejected = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.counter_status === disputeCounterStatus.rejected;
    const isLastCounterFromBuyerAndNotOriginal = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.created_by === userRole.buyerUser && lastCounterData?.counter_status !== disputeCounterStatus.original;
    const isRejectReasonPresent = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && lastCounterData?.reject_reason;
    const initialLineDisputeCounter = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[0] : {};
    const disableSubmitCounter = lastCounterData?.delivery_date 
        ? ((parseFloat(counterQty) === 0 || counterQty === "" || counterQtyUnit === "" || !lastCounterData?.delivery_date || counterQtyError ) ||  !(parseFloat(counterQty) !== lastCounterData?.qty || dayjs(counterDeliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) !== dayjs(lastCounterData?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit)))
        : (parseFloat(counterQty) === 0 || counterQty === "" || counterQtyUnit === "" || counterQty === lastCounterData?.qty || counterQtyError);
    const isLastCounterFromBuyer = lastCounterData?.created_by === userRole.buyerUser;
    const isLastCounterFromSupplier = lastCounterData?.created_by === userRole.sellerUser;
    const isInSubmissionQueue = !!(finalDisputePayload?.order_line_level?.some((item: any) => item?.counter_id === lastCounterData?.counter_id));
    const updateFinalPayload = {...finalDisputePayload};
    if(!updateFinalPayload?.order_line_level) updateFinalPayload.order_line_level = [];
    const disputeList = (isBuyer && isLastCounterFromBuyer && !lastCounterData?.restocking_fee && !initialLineDisputeCounter?.delivery_date) ? watch(`cart_items.${index}.line_dispute_counter`).slice(0, -1) : watch(`cart_items.${index}.line_dispute_counter`);
    const {mutateAsync: getSellerPrices} = usePostGetSellerPrices();
    const disputeCounterScrollRef = useRef<HTMLDivElement>(null);
    const [sellerCounterBtnDisabled, setSellerCounterBtnDisabled] = useState(false);

    useEffect(() => {
        if (disputeCounterScrollRef.current && disputeList?.length > 0) {
            disputeCounterScrollRef.current.scrollTop = disputeCounterScrollRef.current.scrollHeight;
        }
    }, [disputeList]);
    
    const handleCancelDropdownClick = (event: React.MouseEvent<HTMLElement>) => {
        setCancelDropdownAnchorEl(event.currentTarget);
        setIsCancelDropdownOpen(!isCancelDropdownOpen);
    };

    const handleCancelDropdownClose = () => {
        setIsCancelDropdownOpen(false);
        setCancelDropdownAnchorEl(null);
    };

    const handleCounterClick = () => {
        const lastCounter = watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 ? watch(`cart_items.${index}.line_dispute_counter`)[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1] : {};
        setCounterQty(lastCounter?.qty || "");
        setCounterQtyUnit(lastCounter?.qty_unit.toLowerCase() || "");
        setCounterDeliveryDate(lastCounter?.delivery_date || "");
        setIsCounter(true);
    }

    const handleOpenCalendar = () => {
        setIsCalendarOpen(true);
    };

    const handleDateSelect = (date: Date) => {
        // Update the delivery date in the dispute counter
        const formattedDate = dayjs(date).format('YYYY-MM-DD');
        // setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, formattedDate);
        setCounterDeliveryDate(formattedDate);
        setIsCalendarOpen(false);
    };

    let isCounterStatus: string | undefined 
     if (isCounterResolved) {
        isCounterStatus = styles.isCounterResolved;
    } else if (isCounterPending) {
        isCounterStatus = styles.isCounterPending;
    } else if (isCounterRejected) {
        isCounterStatus = styles.isCounterRejected;
    } else {
        isCounterStatus = "";
    }

    const handleAcceptCounter = async () => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer or seller accept or reject a dispute for line qty change
                            "po_line":  initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": "accept", //reject 
                            "counter_id": lastCounterData?.counter_id,
                        }
                    ]
                }
            }
            handleUpdateDisputePayload(payload);
            if(isBuyer){
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                }
            }
            removeFromAttentionItems(actualIndex, "line_item");
        }catch(error){
            console.log("handleAcceptCounter error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }

    const handleCancelLine = async () => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer  cancel line 
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": "cancel_line",
                            "counter_id": lastCounterData?.counter_id,
                        }
                    ]
                }
            }
            console.log("handleCancelLine payload @>>>>>>>", payload);
            handleUpdateDisputePayload(payload);
            const response = await disputeOrderMutation(payload);
            if(response?.data?.error_message){
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
            removeFromAttentionItems(actualIndex, "line_item");
        }catch(error){
            console.log("handleCancelLine error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }

    const handleRevertToOriginal = async () => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer  revert to original 
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": "revert_to_original",
                            "counter_id": lastCounterData?.counter_id,
                        }
                    ]
                }
            }
            console.log("handleRevertToOriginal payload @>>>>>>>", payload);
            
            handleUpdateDisputePayload(payload);
            const response = await disputeOrderMutation(payload);
            if(response?.data?.error_message){
                showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                return;
            }
            removeFromAttentionItems(actualIndex, "line_item");
        }catch(error){
            console.log("handleRevertToOriginal error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }

    const handleSubmitCounter = async () => {
        try{
            setSellerCounterBtnDisabled(true);
            const currentCounterList = watch(`cart_items.${index}.line_dispute_counter`);
            const originalCounters = currentCounterList.filter((item: any) => !item?.new_counter);
            const lastOrgCounterObj = originalCounters[originalCounters.length - 1];
            const isLastOrgCounterFromSupplier = lastOrgCounterObj?.created_by === userRole.sellerUser;

            const deliveryDate = (lastOrgCounterObj?.delivery_date && counterDeliveryDate && dayjs(lastOrgCounterObj?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit) !== dayjs(counterDeliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit)) && dayjs(counterDeliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer counter a dispute for line qty change
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "qty": parseFloat(counterQty),
                            "qty_unit": counterQtyUnit.toUpperCase(),
                            "delivery_date": deliveryDate,
                            "action": (isSupplier && isLastOrgCounterFromSupplier) ? "edit_counter" : "counter", // edit_counter
                            "counter_id": lastCounterData?.counter_id,
                        }
                    ]
                }
            }
            console.log("handleSubmitCounter payload @>>>>>>>", payload);
            if(isBuyer){
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                }
            }else{
 
                const lastCounterNumber = originalCounters.length > 0 
                    ? Math.max(...originalCounters.map((item: any) => Number(item.counter_number)))
                    : Number(lastCounterData?.counter_number || 0);

                const lineitem = watch(`cart_items.${index}`);
                const getPricePayload = {
                    "data": {
                        "po_number": orderManageMentInitialData?.seller_po_number,
                        "order_line_level": [
                            { "po_line":Number(initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`)),
                                "qty": parseFloat(counterQty),
                                "qty_unit": counterQtyUnit.toUpperCase(),
                                "product_id":  lineitem?.product_id,
                                "price_unit":  lineitem?.price_unit.toUpperCase()
                            }
                        ]
                    }
                };
                const prices = await getSellerPrices(getPricePayload);
                const newCounterData = {
                    ...lastCounterData,
                    ...payload?.data?.order_line_level[0],
                    delivery_date: counterDeliveryDate,
                    counter_number: isLastOrgCounterFromSupplier ? String(lastCounterNumber) : String(lastCounterNumber + 1),
                    created_by: userRole.sellerUser,
                    ...(prices?.seller_price_per_unit !== undefined && { buyer_price_per_unit: Number(prices.seller_price_per_unit).toFixed(4) }),
                    ...(prices?.seller_line_total !== undefined && { buyer_line_total: Number(prices.seller_line_total).toFixed(4) }),
                    new_counter: true
                };
                
                const updatedCounterList = currentCounterList.some((item: any) => (item.new_counter))
                    ? currentCounterList.map((item: any) => (item.new_counter) ? newCounterData : item)
                    : isLastOrgCounterFromSupplier 
                        ? [...currentCounterList.slice(0, -1), newCounterData]
                        : [...currentCounterList, newCounterData];
                if(!lastCounterData?.new_counter){
                    setValue(`cart_items.${index}.prev_line_dispute_counter`, watch(`cart_items.${index}.line_dispute_counter`));
                }
                setValue(`cart_items.${index}.line_dispute_counter`, updatedCounterList);
                setCreatePoResultCopy((prev: any) => ({
                    ...prev,
                    [index]: { ...prev[index], line_dispute_counter: updatedCounterList }
                }));
            }
            handleUpdateDisputePayload(payload);
            if(deliveryDate) setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, deliveryDate);
            removeFromAttentionItems(actualIndex, "line_item");
            setIsCounter(false);
            setCounterQty("");
            setCounterQtyUnit("");
            setCounterDeliveryDate("");
        } catch(error){
            console.log("handleSubmitCounter error @>>>>>>>", error);
            showCommonDialog(null,error?.message || commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        } finally {
            setSellerCounterBtnDisabled(false);
        }
    }

    const handleSellerRejectCounter = () => {
        const payload = {
            "data": {
                "po_number": orderManageMentInitialData?.buyer_po_number,
                "order_line_level": [
                    { // seller reject a dispute for line qty change
                        "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                        "action": "reject",
                        "counter_id": lastCounterData?.counter_id,
                    }
                ]
            }
        };
        console.log("handleSellerRejectCounter payload @>>>>>>>", payload);
        handleUpdateDisputePayload(payload);
        removeFromAttentionItems(actualIndex, "line_item")
    }

    const handleOpenAcceptCancelLineModal = () => {
        setRestockingFee(lastCounterData?.restocking_fee || "0");
        setIsAcceptCancelLineModalOpen(true);
    }

    const handleRejectCancelLine = () => {
        const payload = {
            "data": {
                "po_number": orderManageMentInitialData?.buyer_po_number,
                "order_line_level": [
                    { // seller reject a cancel line
                        "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                        "action": "reject",
                        "counter_id": lastCounterData?.counter_id,
                    }
                ]
            }
        };
        console.log("handleRejectCancelLine payload @>>>>>>>", payload);
        handleUpdateDisputePayload(payload);
    }

    const handleSkipAcceptCancelLine = () => {
        const payload = {
            "data": {
                "po_number": orderManageMentInitialData?.buyer_po_number,
                "order_line_level": [
                    { // seller counter cancel line with restocking fee 
                        "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                        "qty": 0,
                        "qty_unit": initialLineDisputeCounter?.qty_unit,
                        "action": "accept",
                        "counter_id": lastCounterData?.counter_id
                    }
                ]
            }
        };
        console.log("handleSkipAcceptCancelLine payload @>>>>>>>", payload);
        handleUpdateDisputePayload(payload);
        setIsAcceptCancelLineModalOpen(false);
    }

    const handleSubmitAcceptCancelLine = async() => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer/seller counter a cancel line
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": "counter",
                            "counter_id": lastCounterData?.counter_id,
                            "qty": 0,
                            "qty_unit": initialLineDisputeCounter?.qty_unit,
                            "restocking_fee": restockingFee
                        }
                    ]
                }
            }
            console.log("handleSubmitAcceptCancelLine payload @>>>>>>>", payload);
            handleUpdateDisputePayload(payload);
            if(isBuyer){
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                }   
                setRestockingFee("");
            }
            setIsAcceptCancelLineModalOpen(false);
        } catch(error){
            console.log("handleSubmitAcceptCancelLine error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }

    const handleEditRestockingFee = () => {
        setIsAcceptCancelLineModalOpen(true);
    }

    const handleBuyerAcceptRestockingFee = async() => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer/seller accept cancel line with restocking fee 
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": "accept",
                            "counter_id": lastCounterData?.counter_id,
                            "qty": 0,
                            "qty_unit": initialLineDisputeCounter?.qty_unit
                        }
                    ]
                }
            }
            console.log("handleSubmitAcceptCancelLine payload @>>>>>>>", payload);
            handleUpdateDisputePayload(payload);
            if(isBuyer){
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                }
            }
            removeFromAttentionItems(actualIndex, "line_item");
            setIsAcceptCancelLineModalOpen(false);
            setRestockingFee("");
        } catch(error){
            console.log("handleBuyerAcceptRestockingFee error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }

    }

    const handleBuyerRescindRestockingFee = async(action: string) => {
        try{
            const payload = {
                "data": {
                    "po_number": orderManageMentInitialData?.buyer_po_number,
                    "order_line_level": [
                        { // buyer/seller rescind cancel line with restocking fee 
                            "po_line": initialLineDisputeCounter?.po_line || watch(`cart_items.${index}.po_line`),
                            "action": action,
                            "counter_id": lastCounterData?.counter_id,
                            "qty": 0,
                            "qty_unit": initialLineDisputeCounter?.qty_unit
                        }
                    ]
                }
            }
            console.log("handleBuyerRescindRestockingFee payload @>>>>>>>", payload);
            handleUpdateDisputePayload(payload);
            if(isBuyer){
                const response = await disputeOrderMutation(payload);
                if(response?.data?.error_message){
                    showCommonDialog(null, response?.data?.error_message, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
                    return;
                }
            }
            removeFromAttentionItems(actualIndex, "line_item");
            setIsAcceptCancelLineModalOpen(false);
            setRestockingFee("");
        } catch(error){
            console.log("handleBuyerRescindRestockingFee error @>>>>>>>", error);
            showCommonDialog(null, commomKeys.errorContent, null, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }]);
            return;
        }
    }

    const handleCounterQtyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setCounterQty(e.target.value);
        handlePriceCalculation(e.target.value, counterQtyUnit);
    }

    const handlePriceCalculation = async (qty: string, qtyUnit: string) => {
        try{
            const qtyVal = parseFloat(qty);
            const orderIncrement = getValUsingUnitKey(watch(`cart_items.${index}.descriptionObj`), qtyUnit, orderIncrementPrefix);
            if(!(qtyVal > 0 && getFloatRemainder(qtyVal, orderIncrement) === 0)){
                setCounterQtyError(true);
            } else {
                setCounterQtyError(false);
            }
        } catch(error){
            console.log("handlePriceCalculation error @>>>>>>>", error);
        }
    }

    const handleCancelCounter = () => {
        setCounterQty("");
        setCounterQtyUnit("");
        setCounterQtyError(false);
        setIsCounter(false);
        setCounterDeliveryDate("");
    }

    const handleUpdateDisputePayload = (payload: any) => {
        const existingLineIndex = updateFinalPayload.order_line_level.findIndex(
            (line: any) => line.po_line === payload.data.order_line_level[0].po_line
        );

        if (existingLineIndex !== -1) {
            // Update existing line data
            updateFinalPayload.order_line_level[existingLineIndex] = {
                ...updateFinalPayload.order_line_level[existingLineIndex],
                ...payload.data.order_line_level[0]
            };
        } else {
            // Add new line data
            updateFinalPayload.order_line_level.push(payload.data.order_line_level[0]);
        }
        setFinalDisputePayload({...updateFinalPayload});
    }

    const hasMatchingAction = (actionToCheck: string) => {
        if (
          finalDisputePayload &&
          Array.isArray(finalDisputePayload.order_line_level)
        ) {
          return finalDisputePayload.order_line_level.some((item: any) => {
            return item.po_line === watch(`cart_items.${index}.po_line`) && item.action === actionToCheck;
          });
        }
        return false;
      }

    const handleUndoAction = () => {
        const currentLineItem = watch(`cart_items.${index}`);
        const lastCounterData = currentLineItem.line_dispute_counter[currentLineItem.line_dispute_counter.length - 1];
        if(lastCounterData?.new_counter){
            setValue(`cart_items.${index}.line_dispute_counter`, currentLineItem.prev_line_dispute_counter);
            setCreatePoResultCopy((prev: any) => ({
                ...prev,
                [index]: { ...prev[index], line_dispute_counter: currentLineItem.prev_line_dispute_counter }
            }));
        }
        setRestockingFee("");
        setFinalDisputePayload({...finalDisputePayload, order_line_level: finalDisputePayload.order_line_level.filter((item: any) => item.po_line !== watch(`cart_items.${index}.po_line`))});
        addToAttentionItems({ id: actualIndex, type: "line_item" });
    }
    return (
        <tr className={clsx(styles.marginBottom, 
          isCounterResolved && styles.disputeResolved,
          isCounterPending && styles.disputePending, 
          isCounterRejected && styles.disputeRejected )} id={`disputeTile-${actualIndex}`} >
            <Tooltip 
                title={(isStateZipValChange && isBuyer) ? "You cannot make changes until the shipping address conflict is resolved or rejected by the supplier." : ""}
                placement="top"
                disableInteractive
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 200 }}
                classes={{
                    tooltip: 'inputTooltip',
                }}
                PopperProps={{
                    style: {
                        zIndex: 9999,
                    }
                }}
            >
            <td>
            {currentFocusedItem?.id === actualIndex && (
                <span className={styles.pointRight}>
                  <PointRightIcon />
              </span>
            )}
                <div className={styles.prodId}>
                    {/* <span className={styles.numberContainer}>{index + 1}</span> */}
                    <div className={styles.domesticMaterialCheckbox} >
                        <label className={styles.lineNumberContainer} data-hover-video-id={"domestic-only-po"}>
                            <input
                                type="checkbox"
                                {...register(`cart_items.${index}.domesticMaterialOnly`)}
                                checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                disabled={true}
                                // onChange={(e) => {
                                //     if (!watch('isEdit') || !showDomesticMaterialOnly || !(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') {
                                //         return; // Prevent changes when visually disabled
                                //     }
                                //     register(`cart_items.${index}.domesticMaterialOnly`).onChange(e);
                                //     setIsCreatePoDirty(true);
                                //     saveUserActivity();
                                // }}
                                className={styles.hiddenCheckbox}
                                // We keep it enabled for keyboard navigation but mark it as aria-disabled
                                aria-disabled={true}
                            />
                            <span
                                className={clsx(
                                    styles.customNumberToggle,
                                    styles.disabled,
                                    watch(`cart_items.${index}.domesticMaterialOnly`) ? styles.active : "",
                                    // (!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') ? styles.disabled : "",
                                    // watch(`cart_items.${index}.line_status`) === 'SKIPPED' ? styles.domesticSkipDisabled : ""
                                )}
                                role="checkbox"
                                id={"domesticMaterialCheckbox-" + actualIndex}
                                aria-checked={watch(`cart_items.${index}.domesticMaterialOnly`) ?? false}
                                aria-disabled={true}
                            // ref={lineNumberRef}
                            // tabIndex={(!watch('isEdit') || !showDomesticMaterialOnly || (!(watch(`cart_items.${index}.descriptionObj`) && Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0)) || watch(`cart_items.${index}.line_status`) === 'SKIPPED') ? -1 : 0}
                            >
                                {actualIndex + 1}
                            </span>
                            <span className={clsx(styles.usaOnlyText, watch(`cart_items.${index}.domesticMaterialOnly`) ? "" : styles.visibilityHidden)}>USA<br />ONLY</span>
                        </label>
                    </div>
                </div>
                
            </td>
            <td className={styles.descriptionDispute}>
                <div className={clsx(styles.poDescriptionDiv, styles.disabled)} >
                    <p className={styles.descriptionModeDisabled}>
                        <textarea
                            // data-hover-video-id={orderInfoIsFilled ? "entering-a-line-in-po" : ""}
                            // type="text"
                            name={register(`cart_items.${index}.descriptionObj`).name}
                            id={`productDescription-${actualIndex}`}
                            value={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                            placeholder={watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.toUpperCase() || ''}
                            className={clsx(styles.poDescription, (watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.length > 0) && styles.poDescriptionFirstLine,
                                (LargeProductsNameList?.find((largeProductsName) => watch(`cart_items.${index}.descriptionObj`)?.UI_Description?.split("\n")[0]?.includes(largeProductsName))) && styles.poDescriptionFirstLine1
                            )}
                            // onClick={() => {
                            //     setIsDescriptionModeEnabled(true);
                            // }}
                            onFocus={() => {
                                // setIsHovering(true);
                                // setIsDescriptionModeEnabled(true)
                                // openAddLineTab()
                                // setUndoStackObject({ name: `descriptionObj`, value: watch(`cart_items.${index}.descriptionObj`), id: `cart_items.${index}.descriptionObj` });
                            }}
                            onBlur={(e) => {
                                // setIsHovering(false);
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Tab' && e.shiftKey) {
                                    // const isCheckboxDisabled = !showDomesticMaterialOnly ||
                                    //     !(watch(`cart_items.${index}.descriptionObj`) &&
                                    //         Object.keys(watch(`cart_items.${index}.descriptionObj`)).length > 0);

                                    // Skip focusing the domestic material checkbox if it's disabled
                                    // if (isCheckboxDisabled) {
                                    //     return; // Let default tab behavior work
                                    // }

                                    // e.preventDefault();
                                    // if (lineNumberRef && lineNumberRef.current) {
                                    //     setIsHovering(false);
                                    //     setIsDomesticFocused(true);
                                    //     lineNumberRef.current.focus();
                                    // }

                                    // setOpenDeliveryToDialog(false);
                                    // setTimeout(() => {
                                    //   const descriptionInput = document.querySelector('input[name="shipping_details.zip"]');
                                    //   if (descriptionInput instanceof HTMLElement) {
                                    //     descriptionInput.focus();
                                    //   }
                                    // }, 100);
                                }
                            }}
                            disabled={true}
                            readOnly
                        />
                    </p>
                    {watch(`cart_items.${index}.descriptionObj`)?.UI_Description &&
                        <div className={styles.partNumberFiled}
                            data-hover-video-id="part-number-po">
                            <InputWrapper>
                                <CustomTextField
                                    // dataHoverVideoId="part-number-po"
                                    type='text'
                                    register={register(`cart_items.${index}.product_tag`)}
                                    disabled={true}
                                    placeholder="Add YOUR PART #"
                                    // className={clsx({ [styles.errorInput]: errors?.cart_items?.[index]?.product_tag?.message })}
                                    value={watch(`cart_items.${index}.product_tag`) ?? ""}
                                // onmouseenter={() => {
                                //     setIsHovering(true);
                                // }}
                                // onFocus={() => {
                                //     setIsHovering(true);
                                //     setIsDescriptionModeEnabled(false);
                                //     setUndoStackObject({ name: `product_tag`, value: watch(`cart_items.${index}.product_tag`), id: `cart_items.${index}.product_tag` });
                                // }}
                                // onBlur={(e) => {
                                //     register(`cart_items.${index}.product_tag`).onBlur(e);
                                //     saveUserActivity();
                                //     setIsHovering(false);
                                //     if (handleStoreUndoStack && isDataChanged) {
                                //         handleStoreUndoStack({ ...undoStackObject, currentValue: e.target.value, from: "line", actualIndex: actualIndex, index: index, uniqueId: actualIndex });
                                //         setIsDataChanged(false);
                                //     }
                                //     setUndoStackObject({});
                                //     setIsQtyInEditMode(true);
                                // }}
                                // onChange={(e) => {
                                //     setIsDataChanged(true);
                                // }}
                                // onKeyUp={(e) => {
                                //     setValue(
                                //         `cart_items.${index}.product_tag`,
                                //         e.target.value
                                //     );
                                // }}
                                />
                            </InputWrapper>
                        </div>
                    }

                </div>
            </td>
            <td colSpan={3} className={styles.disputetblCol}>
                <div className={styles.disputeContainer}>
                    <div className={styles.disputeCounterHeader}>

                    <div className={clsx(styles.disputeCounterContainer1,styles.disputeContainerFirst)}>
                        <div className={styles.disputeCounterScroll} ref={disputeCounterScrollRef}>
                            {disputeList?.length > 0 &&
                                disputeList?.map((dispute: any, i: number) => {
                                    const isMe = userData?.data?.type === dispute?.created_by;
                                    const isDisputeBuyer = dispute?.created_by === userRole.buyerUser;
                                    const isDisputeSupplier = dispute?.created_by === userRole.sellerUser;
                                    return (
                                        <div className={styles.disputeCounterData} key={dispute.counter_number + i} >
                                            <span className={styles.disputeCounterDataFirstCol}>
                                            <span className={styles.disputeCounterStatus}>
                                                <span className={styles.disputeCounterStatusLbl}>
                                                    {( i === watch(`cart_items.${index}.line_dispute_counter`)?.length - 1) ? 
                                                        <span className={styles.disputeCounterStatusIcon}> 
                                                            {isCounterResolved ? <IconResolve/> : isCounterPending ? <IconPending/> : isCounterRejected ? <IconReject/> : ''} 
                                                        </span> 
                                                        : ''}
                                                    {(i=== 0 || !dispute?.created_by) ? "Original " : isMe ? "Me " : isDisputeBuyer ? "Buyer" : isDisputeSupplier && "Supplier"}
                                                </span>
                                                {(i!== 0) && `(${dispute.counter_number})`}
                                            </span>
                                            <span className={styles.disputeCounterQtyUnit}>
                                                <span className={ clsx((i === watch(`cart_items.${index}.line_dispute_counter`)?.length - 1) ? isCounterStatus : '') }>{isSupplier ? formatToTwoDecimalPlaces(dispute.qty) : dispute.qty}</span>
                                                <span className={styles.disputeQtyUnit}>{dispute.qty_unit}</span>
                                            </span>
                                            </span>
                                            <span className={styles.disputeCounterPriceUnit}>
                                                {
                                                    isSupplier ? dispute?.price_unit?.toLowerCase() === priceUnits.lb ? format4DigitAmount(dispute.buyer_price_per_unit) : formatToTwoDecimalPlaces(dispute.buyer_price_per_unit) :
                                                        dispute.buyer_price_per_unit
                                                } <span className={styles.disputePriceUnit}>{dispute.price_unit}</span></span>
                                            <span className={styles.disputeCounterExtended}>{ isSupplier ? formatToTwoDecimalPlaces(dispute.buyer_line_total) : dispute.buyer_line_total}</span>
                                        </div>
                                    )
                                })
                            }
                         </div>
                    </div>
                    {(initialLineDisputeCounter?.delivery_date && lastCounterData?.delivery_date && !isCounter) ?
                    (
                        <div className={styles.deliveryNewLineContainer}>
                            <div className={styles.deliveryNewLineData}>
                                <span className={styles.deliveryDateLbl}>Deliver New Line by:</span>
                                <span className={clsx((watch(`cart_items.${index}.line_dispute_counter`)?.length > 1) && styles.deliveryDateStrike)}>{dayjs(initialLineDisputeCounter?.delivery_date).format('ddd, MMMM D, YYYY')}</span>
                            </div>
                            {watch(`cart_items.${index}.line_dispute_counter`)?.length > 1 && (
                            <div className={styles.deliveryNewLineData}>
                                <span>
                                    <span>
                                        {userData?.data?.type === lastCounterData?.created_by ? "Me " : isLastCounterFromBuyer ? "Buyer" : isLastCounterFromSupplier ? "Supplier" : "Neutral"}
                                        {lastCounterData?.counter_status !== disputeCounterStatus.original && `(${lastCounterData?.counter_number})`}
                                     </span>
                                     <span></span>
                                </span>
                                <span>{dayjs(lastCounterData?.delivery_date).format('ddd, MMMM D, YYYY')}</span>
                            </div>
                            )}
                            <div></div>
                        </div>
                    ) 
                    : (watch(`cart_items.${index}.line_dispute_counter`)?.length > 0 && isLastCounterFromBuyerAndNotOriginal && isBuyer && !lastCounterData?.restocking_fee) &&
                    (
                        <div className={clsx(styles.disputeCounterContainer1,styles.disputeContainerLast)} key={watch(`cart_items.${index}.line_dispute_counter`)?.[watch(`cart_items.${index}.line_dispute_counter`)?.length - 1]?.counter_number}>
                            <div className={styles.disputeCounterData}>
                                 <span className={styles.disputeCounterDataFirstCol}>
                                    <span className={styles.disputeCounterStatus}>
                                        {(isBuyer && isLastCounterFromBuyer) ?
                                            <span className={styles.disputeCounterStatusIcon}>
                                                {isCounterResolved ? <IconResolve /> : isCounterPending ? <IconPending /> : isCounterRejected ? <IconReject /> : ''}
                                            </span>
                                            : ''
                                        }
                                        {userData?.data?.type === lastCounterData?.created_by ? "Me " : isLastCounterFromBuyer ? "Buyer" : isLastCounterFromSupplier ? "Supplier" : "Neutral"}
                                        {lastCounterData?.counter_status !== disputeCounterStatus.original && `(${lastCounterData?.counter_number})`}
                                    </span>
                                    <span className={styles.disputeCounterQtyUnit}>
                                        <span className={clsx((isBuyer && isLastCounterFromBuyer) ? isCounterStatus : '')}>{lastCounterData?.qty}</span>
                                        <span className={styles.disputeQtyUnit}>{lastCounterData?.qty_unit}</span>
                                    </span>
                                </span>
                                <span className={styles.disputeCounterPriceUnit}>{lastCounterData?.buyer_price_per_unit} <span className={styles.disputePriceUnit}>{lastCounterData?.price_unit}</span></span>
                                <span className={styles.disputeCounterExtended}>{lastCounterData?.buyer_line_total}</span>
                            </div>
                        </div>
                    )}
                    {
                      watch(`cart_items.${index}.line_delivery_date`) && (
                        <div className={styles.lineAcceptedDeliveryDateContainerDispute}>
                          <span className={styles.deliveryDateLbl}>Deliver New Line by:</span>
                          <span className={styles.deliveryDate}>{dayjs(watch(`cart_items.${index}.line_delivery_date`)).format('ddd, MMMM D, YYYY')}</span>
                        </div>
                      )
                    }  
                    </div>
                    <div className={styles.disputeCounterBtnSection}>
                        {isCounter ? (
                            <div className={styles.disputeCounterInputGrid}>
                                <div className={styles.disputeInputGridMain}>
                                    <div className={clsx(styles.disputeInputGrid, styles.disputeBox)}>
                                        <Tooltip
                                            title={(counterQtyError && watch(`cart_items.${index}.descriptionObj`)?.UI_Description) &&
                                                `Quantity can only be multiples of ${getValUsingUnitKey(watch(`cart_items.${index}.descriptionObj`), counterQtyUnit, orderIncrementPrefix)}`
                                            }
                                            arrow
                                            placement={"bottom-start"}
                                            disableInteractive
                                            TransitionComponent={Fade}
                                            TransitionProps={{ timeout: 200 }}
                                            classes={{
                                                tooltip: "inputQtyTooltip",
                                            }}
                                        >
                                            <div>
                                                <InputWrapper>
                                                    <CustomTextField
                                                        type='number'
                                                        disabled={false}
                                                        placeholder="Enter Counter Qty"
                                                        value={counterQty ?? ""}
                                                        onChange={handleCounterQtyChange}
                                                        errorInput={counterQtyError}
                                                        mode={(counterQtyUnit?.toLowerCase() === priceUnits.pc) ? "wholeNumber" : "number"}
                                                    />
                                                </InputWrapper>
                                            </div>
                                        </Tooltip>
                                        <div className={styles.selectQtyUnitDispute}>
                                            <Select
                                                value={counterQtyUnit}
                                                onChange={(e: SelectChangeEvent<string>) => {
                                                    setCounterQtyUnit(e.target.value);
                                                    handlePriceCalculation(counterQty, e.target.value);
                                                }}
                                                className={clsx(styles.uomDrodown, 'qtyUnitDropdown')}
                                                MenuProps={{
                                                    classes: {
                                                        paper: styles.selectUomPaper,
                                                    },
                                                }}
                                            >
                                                {watch(`cart_items.${index}.qty_um`)?.map((x: string) => {
                                                    console.log(counterQtyUnit);
                                                    return (
                                                        <MenuItem key={x} value={x}>{x.toUpperCase()}</MenuItem>
                                                    )
                                                })}
                                            </Select>
                                        </div>
                                    </div>
                                    {lastCounterData?.delivery_date && (
                                        <div className={clsx(styles.deliveryDateContainer, styles.disputeBox)}>
                                            <span>Deliver New Line by:</span>
                                            <div className={styles.deliveryDateContainerData}>
                                                <span
                                                    ref={calendarAnchorRef}
                                                    className={styles.clickableDeliveryDate}
                                                    onClick={handleOpenCalendar}
                                                    style={{ cursor: 'pointer' }}
                                                >
                                                    {dayjs(counterDeliveryDate).format('ddd, MMMM D, YYYY')}
                                                </span>
                                                <Popover
                                                    open={isCalendarOpen}
                                                    anchorEl={calendarAnchorRef.current}
                                                    onClose={() => setIsCalendarOpen(false)}
                                                    anchorOrigin={{
                                                        vertical: 'top',
                                                        horizontal: 'center',
                                                    }}
                                                    transformOrigin={{
                                                        vertical: 'bottom',
                                                        horizontal: 'center',
                                                    }}
                                                    classes={{
                                                            paper: styles.disputeCalendarPopover
                                                        }}
                                                >
                                                        <Calendar
                                                            value={counterDeliveryDate || ''}
                                                            setValue={(field: string, value: string) => {
                                                                if (field === 'delivery_date') {
                                                                    // setValue(`cart_items.${index}.line_dispute_counter.${watch(`cart_items.${index}.line_dispute_counter`)?.length - 1}.delivery_date`, value);
                                                                    setCounterDeliveryDate(value);
                                                                }
                                                            }}
                                                            isCalendarOpen={isCalendarOpen}
                                                            setIsCalendarOpen={setIsCalendarOpen}
                                                            disableDeliveryDate={false}
                                                            handleOpenCalendar={handleOpenCalendar}
                                                            saveUserActivity={() => { }}
                                                            saveBomHeaderDetails={() => { }}
                                                            onDateSelect={handleDateSelect}
                                                            allowedDates={deliveryAllowedDates}
                                                            parentClassName={isCalendarOpen}
                                                        />
                                                </Popover>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                <div className={styles.disputeCounterInputBtnGrid}>
                                    <button className={styles.btnAcceptCounter} onClick={handleCancelCounter} disabled={isStateZipValChange}>Cancel Counter</button>
                                    <button className={styles.btnAcceptCounter} onClick={handleSubmitCounter} disabled={disableSubmitCounter || sellerCounterBtnDisabled}>{isSupplier ? "Save Counter" : "Submit"}</button>
                                </div>
                            </div>
                        ) : (isAcceptCancelLineModalOpen) ? (
                            <div className={styles.disputeCounterRestockingFee}>
                                <div className={styles.disputeCounterRestockingFeeInput}>
                                    <span className={styles.restockingFeeLbl}>Restocking Fee  $</span>
                                    <InputWrapper>
                                        <CustomTextField
                                            type='number'
                                            disabled={false}
                                            placeholder=""
                                            value={restockingFee}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                                setRestockingFee(e.target.value);
                                            }}
                                        />
                                    </InputWrapper>
                                </div> 
                                <div className={styles.disputeCounterBtnSkip}>
                                    {isSupplier && <button onClick={handleSkipAcceptCancelLine} className={styles.skipBtn}>Skip</button>}
                                    <button onClick={handleSubmitAcceptCancelLine} className={styles.submitBtn} disabled={(!Number(restockingFee) || restockingFee === lastCounterData?.restocking_fee)}>Submit</button>
                                </div>
                            </div>
                        ) : (
                            <div>
                                {(isBuyer && !isStateZipValChange) && (
                                    <div>
                                        {isCounterResolved ? (
                                            <div className={styles.actionRequiredContainer}>
                                                <span>No action required.</span>
                                                <span>The supplier has accepted your request.</span>
                                            </div>
                                        ) : isRejectReasonPresent ? (
                                            <div className={styles.rejectReasonContainer}>
                                                    <span>{lastCounterData?.reject_reason}</span>
                                            </div>
                                        ) : lastCounterData?.counter_status === disputeCounterStatus.cancelled ? (
                                             <div className={styles.cancelRequestSentContainer}>
                                                <span> Cancellation request has been sent to Supplier. </span>
                                            </div>
                                        ) : (lastCounterData?.created_by === userRole.buyerUser && (lastCounterData?.restocking_fee && parseFloat(lastCounterData?.restocking_fee) > 0)) ? (
                                            <div className={styles.restockingFeeContainerBuyer1}>
                                                <div className={styles.row1}>
                                                    <span>Supplier</span>
                                                    <span>{watch(`cart_items.${index}.line_dispute_counter`)?.[watch(`cart_items.${index}.line_dispute_counter`)?.length - 2]?.restocking_fee}</span>
                                                    <span>restocking fee</span>
                                                </div>
                                                 <div className={styles.row1}>
                                                    <span>Me</span>
                                                    <span>{lastCounterData?.restocking_fee}</span>
                                                    <span>restocking fee</span>
                                                </div>
                                            </div>
                                        ) : (lastCounterData?.created_by === userRole.sellerUser && lastCounterData?.restocking_fee && parseFloat(lastCounterData?.restocking_fee) > 0) ? (
                                            <div className={styles.restockingFeeContainerBuyer}>
                                                <div className={styles.restockingFeeBuyerInner}>
                                                    <span>This line cancelation incurred the following restocking fee from the supplier:</span> 
                                                    <span className={styles.restockingFeeAmount}>{lastCounterData?.restocking_fee}</span>
                                                </div>
                                                <div className={clsx(styles.restockingFeeBuyerBtn,styles.disputeCounterBtnGrid)}>
                                                    <button className={styles.btnAcceptCounter} onClick={handleBuyerAcceptRestockingFee} disabled={isStateZipValChange}>Accept Fee</button>
                                                    <button className={styles.btnAcceptCounter} onClick={handleOpenAcceptCancelLineModal} disabled={isStateZipValChange}>Counter</button>
                                                    <button className={styles.btnAcceptCounter} onClick={() => handleBuyerRescindRestockingFee("revert_to_original")} disabled={isStateZipValChange}>Rescind Cancel</button>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className={styles.disputeCounterBtnGrid}>
                                                <button
                                                    className={styles.btnAcceptCounter}
                                                    onClick={handleAcceptCounter}
                                                    disabled={(isEditingPo || lastCounterData?.created_by === userRole.buyerUser || isStateZipValChange)}
                                                >
                                                    Accept
                                                </button>
                                                <button className={styles.btnAcceptCounter} onClick={handleCounterClick} disabled={isEditingPo || isStateZipValChange}>
                                                    Counter
                                                </button>
                                                
                                                    {initialLineDisputeCounter?.delivery_date ? (
                                                        <button
                                                            className={styles.btnAcceptCounter}
                                                            onClick={handleCancelLine}
                                                            disabled={isEditingPo || isStateZipValChange}
                                                        >
                                                            <span>Cancel</span>
                                                        </button>
                                                    ) : (
                                                        <>
                                                        <div className={styles.exportContainer}>
                                                            <button
                                                                className={clsx(styles.selectedProductHeaderButton, styles.exportButton,isCancelDropdownOpen && styles.cancelBtnOpen)}
                                                                onClick={handleCancelDropdownClick}
                                                                disabled={isEditingPo || isStateZipValChange}
                                                            >
                                                                <span>Cancel</span>
                                                                <span className={styles.exportArrow}><DropDownArrowIcon /></span>
                                                            </button>
                                                            <Popover
                                                                open={isCancelDropdownOpen}
                                                                anchorEl={cancelDropdownAnchorEl}
                                                                onClose={handleCancelDropdownClose}
                                                                anchorOrigin={{
                                                                    vertical: 'top',
                                                                    horizontal: 'right',
                                                                }}
                                                                transformOrigin={{
                                                                    vertical: 'bottom',
                                                                    horizontal: 'right',
                                                                }}
                                                                classes={{
                                                                    paper: styles.exportDropdownPanel
                                                                }}
                                                            >
                                                                <button
                                                                    className={styles.exportOption}
                                                                    onClick={handleCancelLine}
                                                                    disabled={false}
                                                                >
                                                                    Cancel Line
                                                                </button>
                                                                <button
                                                                    className={styles.exportOption}
                                                                    onClick={handleRevertToOriginal}
                                                                    disabled={false}
                                                                >
                                                                    Revert to Original
                                                                </button>
                                                            </Popover>
                                                        </div>
                                                        </>
                                                    )}
                                               

                                            </div>

                                        )}

                                    </div>
                                )}
                                {(isSupplier && !isStateZipValChange) && (
                                    <div className={styles.disputeCounterContainer2}>
                                       {(lastCounterData?.created_by === userRole.buyerUser && lastCounterData?.restocking_fee && parseFloat(lastCounterData?.restocking_fee) > 0) ? (
                                            <div className={styles.restockingFeeContainerBuyer}>
                                                <div className={styles.restockingFeeBuyerInner}>
                                                    <span>This line cancelation incurred the following restocking fee from the buyer:</span> 
                                                    <span className={styles.restockingFeeAmount}>{lastCounterData?.restocking_fee}</span>
                                                </div>
                                                <div className={clsx(styles.restockingFeeBuyerBtn,styles.disputeCounterBtnGrid)}>
                                                    <button className={clsx(styles.btnAcceptCounter,hasMatchingAction("accept") && styles.accepted)} onClick={handleBuyerAcceptRestockingFee} disabled={isStateZipValChange}>Accept Fee</button>
                                                    <button className={styles.btnAcceptCounter} onClick={handleOpenAcceptCancelLineModal} disabled={isStateZipValChange}>Counter</button>
                                                    <button className={clsx(styles.btnAcceptCounter,hasMatchingAction("reject") && styles.rejected)} onClick={() => handleBuyerRescindRestockingFee("reject")} disabled={isStateZipValChange}>Reject</button>
                                                    {
                                                        isInSubmissionQueue && (
                                                            <Tooltip title={undo} arrow 
                                                                        placement="bottom" disableInteractive 
                                                                        TransitionComponent={Fade} TransitionProps={{ timeout: 200 }} 
                                                                        classes={{ tooltip: styles.undoTooltip }}
                                                                >
                                                                    <button onClick={handleUndoAction} className={styles.undoIcon}><UndoIcon /></button>
                                                                </Tooltip>
                                                        )
                                                    }
                                                </div>
                                            </div>
                                        ) : 
                                        ((lastCounterData?.restocking_fee && parseFloat(lastCounterData?.restocking_fee) > 0) || restockingFee?.length > 0) ? (
                                            <div className={styles.disputeLastCounterRestockingFee}>
                                                <span className={styles.restockingFeeLbl}>Buyer is presented with the restocking fee and they must Accept, Counter or Rescind. We will notify you.</span>
                                                <span className={styles.counterStatusQueue}>{isInSubmissionQueue ? disputeStatusDisplayText.inSubmissionQueue : disputeStatusDisplayText.countered}</span>
                                                <div className={styles.restockingFeeOriginal}>
                                                    <span>Original</span>
                                                    <span>${lastCounterData?.restocking_fee || restockingFee} restocking fee</span>
                                                    <span>
                                                     <button className={styles.editFeeBtn} onClick={handleEditRestockingFee} disabled={isStateZipValChange}>Edit Fee</button>
                                                    </span>
                                                    {
                                                        isInSubmissionQueue && (
                                                            <Tooltip title={undo} arrow 
                                                                        placement="bottom" disableInteractive 
                                                                        TransitionComponent={Fade} TransitionProps={{ timeout: 200 }} 
                                                                        classes={{ tooltip: styles.undoTooltip }}
                                                                >
                                                                    <button onClick={handleUndoAction} className={styles.undoIcon}><UndoIcon /></button>
                                                                </Tooltip>
                                                        )
                                                    }
                                                </div>
                                            </div>
                                        ) : (lastCounterData?.event === "cancel_line") ? (
                                            <div className={styles.disputeCounterAcceptReject}>
                                                <span className={styles.disputeAcceptRejectText}>Buyer requests to cancel this line. If you choose to accept this line cancelation, you’ll have the option to apply a restocking fee.</span>
                                                <div className={styles.disputeBtnGrid}> 
                                                    <button onClick={handleOpenAcceptCancelLineModal} disabled={isStateZipValChange}>Accept</button>
                                                    <button onClick={handleRejectCancelLine} disabled={isStateZipValChange}>Reject</button>
                                                    {
                                                        isInSubmissionQueue && (
                                                             <Tooltip title={undo} arrow 
                                                                        placement="bottom" disableInteractive 
                                                                        TransitionComponent={Fade} TransitionProps={{ timeout: 200 }} 
                                                                        classes={{ tooltip: styles.undoTooltip }}
                                                                >
                                                                    <button onClick={handleUndoAction} className={styles.undoIcon}><UndoIcon /></button>
                                                                </Tooltip>
                                                        )
                                                    }
                                                </div>
                                            </div>
                                        ) : (lastCounterData?.created_by === userRole.sellerUser && lastCounterData?.counter_status !== disputeCounterStatus.rejected) ? (
                                            <div className={styles.editCounterContainer}>
                                                <span>Buyer has not replied to your counter yet.  Before they do you can edit/cancel your most recent counter.</span>
                                                <div className={styles.editCounterBtnContainer}>
                                                    <div className={styles.editCounterBtnContainerInner}>
                                                        <button className={styles.btnAcceptCounter} onClick={handleCounterClick} disabled={isStateZipValChange}>Edit Counter</button>
                                                        {
                                                            isInSubmissionQueue && (
                                                                 <Tooltip title={undo} arrow 
                                                                        placement="bottom" disableInteractive 
                                                                        TransitionComponent={Fade} TransitionProps={{ timeout: 200 }} 
                                                                        classes={{ tooltip: styles.undoTooltip }}
                                                                    >
                                                                        <button onClick={handleUndoAction} className={styles.undoIcon}><UndoIcon /></button>
                                                                    </Tooltip>
                                                            )
                                                        }
                                                    </div>
                                                    
                                                    <span className={styles.submitCounterdStatus}>{isInSubmissionQueue ? disputeStatusDisplayText.inSubmissionQueue : disputeStatusDisplayText.countered}</span>
                                                </div>
                                            </div>
                                        ) : (
                                            <div className={clsx(styles.disputeCounterBtnGrid1,lastCounterData?.counter_status === disputeCounterStatus.rejected && styles.disableButtons)}>
                                                {isInSubmissionQueue && <span className={styles.counterStatusQueue}>{disputeStatusDisplayText.inSubmissionQueue}</span>}
                                                {(isLastCounterFromSupplier && isCounterRejected) && <span className={styles.counterStatusQueueRejected}>{disputeStatusDisplayText.rejected}</span>}
                                                {(isLastCounterFromSupplier && !isCounterRejected) && <span className={styles.counterStatusQueue}>{disputeStatusDisplayText.countered}</span>}
                                                <div className={styles.disputeCounterBtnGrid}>
                                                    <button
                                                        className={clsx(styles.btnAcceptCounter,hasMatchingAction("accept") && styles.accepted)}
                                                        onClick={handleAcceptCounter}
                                                        disabled={(isEditingPo || isLastCounterFromSupplier || isStateZipValChange)}

                                                    >
                                                        Accept
                                                    </button>
                                                    <button className={styles.btnAcceptCounter} onClick={handleCounterClick} disabled={isEditingPo || isStateZipValChange}>
                                                        Counter
                                                    </button>
                                                   
                                                        <button className={clsx(styles.btnAcceptCounter,hasMatchingAction("reject") && styles.rejected)}
                                                            onClick={handleSellerRejectCounter}
                                                            disabled={isEditingPo || isLastCounterFromSupplier || isStateZipValChange}
                                                        >
                                                            <span>Reject</span>
                                                        </button>
                                                        {
                                                            isInSubmissionQueue && (
                                                                    <Tooltip title={undo} arrow 
                                                                        placement="bottom" disableInteractive 
                                                                        TransitionComponent={Fade} TransitionProps={{ timeout: 200 }} 
                                                                        classes={{ tooltip: styles.undoTooltip}}
                                                                    >
                                                                        <button onClick={handleUndoAction} className={styles.undoIcon}><UndoIcon /></button>
                                                                    </Tooltip>
                                                            )
                                                        }
                                                </div>
                                            </div>
                                        )}

                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </td >
            </Tooltip>

        </tr >
    )
}

export default DisputeTile
