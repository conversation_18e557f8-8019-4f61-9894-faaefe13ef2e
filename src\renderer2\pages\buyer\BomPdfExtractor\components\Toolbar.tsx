import React, { useState, useEffect, RefObject, ChangeEvent } from 'react';
import { ROTATE, useBomPdfExtractorStore, ZOOM } from '../BomPdfExtractorStore';
import { BOX_TYPES } from '../config/boxTypes';
import styles from '../styles/BomExtractor.module.scss';
import clsx from 'clsx';
import { ReactComponent as ZoomInIcon } from '../../../../assets/New-images/zoom-in.svg';
import { ReactComponent as ZoomOutIcon } from '../../../../assets/New-images/zoom-out.svg';
import { ReactComponent as RotateIcon1 } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/icon-reset1.svg';
import { ReactComponent as RotateIcon2 } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/icon-reset2.svg';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import CustomColumnCreator from './CustomColumnCreator';
import { createActions } from '../utils/BOMUploadUtils';
import OrientationSlider from './OrientationSlider';
import Slider from "./Slider";
import VideoPopup from './VideoPopup';

interface BoxType {
  id: string;
  label: string;
}

interface ToolbarProps {
}

const Toolbar: React.FC<ToolbarProps> = () => {
  const [showMagnifyingGlass, setShowMagnifyingGlass] = useState<boolean>(true);
  const [processClicked, setProcessClicked] = useState<boolean>(false);
  const [notificationMessage, setNotificationMessage] = useState<string>('');
  const [extractClicked, setExtractClicked] = useState<boolean>(false);
  const [overlapPercent, setOverlapPercent] = useState<number>(40);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [orientation, setOrientation] = useState<number>(0);
  const { setExtractText, setAutoSelectColumns,
    setGridOpacity, setCurrentBoxType, currentBoxType, textractRBushInitialized, hasBoxes, setDoResetAll,
    pdfFile, zoomPercentage, setZoomPercentage, setPageRotations, pageRotations,gridOpacity,
    customBoxTypes, setCustomBoxTypes, setCanvasRotation, undoStack, redoStack, setDoUndo, setDoRedo,
    canvasRotation
  } = useBomPdfExtractorStore();
  const boxTypes = [...Object.entries(BOX_TYPES).map(([id, type]) => ({
    id,
    label: type.label,
    selectedBg: type.selectedBg,
    hoverBg: type.hoverBg,
    bgColor: type.bgColor,
    textColor: type.textColor,
    defaultTextColor: type.defaultTextColor,
    hoverTextColor: type.hoverTextColor,
    minWidth: type.minWidth
  })), ...customBoxTypes];

  const { showCommonDialog, resetDialogStore } = useDialogStore();
  const [showCustomColumnCreator, setShowCustomColumnCreator] = useState(false);
  const [showVideoPopup, setShowVideoPopup] = useState(false);

  const showNotification = (message: string) => {
    if (timer) clearTimeout(timer);
    setNotificationMessage(message);
    const tempTimer = setTimeout(() => {
      setNotificationMessage('');
    }, 6000);
    setTimer(prev => {
      if (prev) clearTimeout(prev);
      return tempTimer;
    });
  };

  useEffect(()=>{
    if(pageRotations != null && canvasRotation != null){
        setOrientation(((pageRotations + canvasRotation + 180) % 360) - 180);
    }
  },[pageRotations, canvasRotation]);


  // Handle extract text button click
  const handleExtractTextClick = () => {
    setExtractClicked(true);

    if (!textractRBushInitialized) {
      showNotification("Processing Document, we will extract once completed.");
    } else {
      setNotificationMessage("");
    }
    setExtractText(true);
    //if (onExtractTextClick) onExtractTextClick();
  };

  // Update notification when TextractRBush is initialized
  // useEffect(() => {
  //   if (extractClicked && textractRBushInitialized) {
  //     showNotification("Processing completed, scroll down to view extracted text.");
  //     //if (onExtractTextClick) onExtractTextClick();
  //   }
  // }, [textractRBushInitialized, extractClicked]);//, onExtractTextClick]);

  // Reset extract clicked state when a new PDF is uploaded
  useEffect(() => {
    if (!pdfFile) {
      setExtractClicked(false);
      setNotificationMessage('');
    }
  }, [pdfFile]);

  const handleDialogYesBtn = (newPdfRotation: number, newCanvasRotation: number) => {
    setDoResetAll(true);

    doRotation(newPdfRotation, newCanvasRotation);

    resetDialogStore();
  }

  const doRotation = (newPdfRotation: number, newCanvasRotation: number) => { 
    createActions(ROTATE, {pageRotations, canvasRotation}, {pageRotations:newPdfRotation, canvasRotation:newCanvasRotation});
   

    setPageRotations(newPdfRotation);
    setCanvasRotation(newCanvasRotation);
  }


  const handlePageRotation = (rotation : any) => {
    const pdfRotation = Math.round(rotation / 90) * 90;
  
    // Calculate the remaining canvas rotation (fine adjustment)
    const canvasRotation = rotation - pdfRotation;
    
    // Ensure canvas rotation is within -45 to +45 degrees for best results
    let adjustedCanvasRotation = canvasRotation;
    if (canvasRotation > 45) {
      adjustedCanvasRotation = canvasRotation - 90;
    } else if (canvasRotation < -45) {
      adjustedCanvasRotation = canvasRotation + 90;
    }
    if (hasBoxes) {
      showCommonDialog(
        null,
        "Rotating PDF will clear all drawn boxes. Do you want to continue?",
        null,
        resetDialogStore,
        [
          {
            name: 'Yes',
            action: () => handleDialogYesBtn(pdfRotation, adjustedCanvasRotation)
          },
          {
            name: 'No',
            action: resetDialogStore
          }
        ]
      );
      return;
    }

    doRotation(pdfRotation, adjustedCanvasRotation);
  }

  const zoomChangePercent = 10;
  const minZoom = 20;
  const maxZoom = 200;
  const [hoveredBoxType, setHoveredBoxType] = useState<string | null>(null);

  const handleCreateCustomClick = () => {
    setShowCustomColumnCreator(true);
  };

  const handleCustomColumnSave = (_customBoxType: any) => {
    if(_customBoxType){
      const customBoxType = [...customBoxTypes, _customBoxType];
      setCustomBoxTypes(customBoxType);
    }
    setShowCustomColumnCreator(false);
  };

  // Handle custom column cancel
  const handleCustomColumnCancel = () => {
    setShowCustomColumnCreator(false);
  };

  const handleZoomChange = (newZoom: number) => {
    createActions(ZOOM, {zoomPercentage:zoomPercentage}, {zoomPercentage:newZoom});
    setZoomPercentage(newZoom);
  }

  const getButtonTextColor = (boxType: any) => {
    let color;
    if(currentBoxType === boxType.id){
      color = boxType.textColor;
    }else if(hoveredBoxType === boxType.id){
      color = boxType.hoverTextColor;
    }else{
      color = boxType.defaultTextColor;
    }
    return color;
  } 

  return (
    <div style={{position:'relative'}}>

      <div className={styles.toolbar}>
        <div className={styles.noteBg}>
          <div className={styles.noteText}>
            Click categories below and draw a box around the relevant information in your PDF.
          </div>
          <div className={styles.watchDemo}>
            <span onClick={() => setShowVideoPopup(true)} style={{ cursor: 'pointer' }}>Watch Demo.</span>
          </div>
        </div>
        <div className={styles.toolbarMain}>
          <div className={styles.toolbarTitleContainer}>
            {/* <span className={styles.toolbarTitle}>BOM Data Extractor</span>
        {pdfFileName && <span className={styles.toolbarFilename}>{pdfFileName}</span>} */}
          </div>
          {/* <input
        type="file"
        accept=".pdf"
        onChange={(e) => {
          setProcessClicked(false); // Reset processClicked when a new PDF is uploaded
          setExtractClicked(false); // Reset extractClicked when a new PDF is uploaded
          setNotificationMessage(''); // Clear notification message
          if (onUploadClick) onUploadClick(e);
        }}
        ref={fileInputRef}
        className="file-input"
      /> */}
          {/* <button onClick={() => fileInputRef.current?.click()}>Select PDF</button> */}
          {/* {<button
        onClick={() => {
          setProcessClicked(true);
          setNotificationMessage(''); // Clear notification message
          if (onProcessDocClick) onProcessDocClick();
        }}
        disabled={!pdfFile || processClicked || !isImageBasedPdf}
      >
        Process PDF
      </button>} */}
          {/* <button
        onClick={()=>{setDoUndoBox(true);}}
        disabled={false}
      >
        Undo Box
      </button>
      <button onClick={()=>{setDoResetAll(true);}}>Reset All</button> */}
          {/* <button
        onClick={handleExtractTextClick}
        disabled={![pdfFile]  || (isImageBasedPdf && !processClicked)}
        style={{
          position: 'relative',
          minWidth: '120px',
          cursor: !pdfFile  ? 'not-allowed' : 'pointer'
        }}
      >
        Extract Text
      </button> */}
          {/* <select
        value={currentBoxType}
        onChange={(e) => setCurrentBoxType(e.target.value)}
      >
        {boxTypes.map(type => (
          <option key={type.id} value={type.id}>{type.label}</option>
        ))}
      </select> */}
          <div className={styles.toolbarBtnContainer}>
            <div className={styles.toolbarLeftSection}>
              <div className={styles.boxTypeButtons} role="radiogroup" aria-label="Box type selection">
                {boxTypes.map((boxType) => (
                  <button
                    key={boxType.id}
                    className={clsx(styles.boxTypeButton)}
                    onClick={() => setCurrentBoxType(boxType.id)}
                    disabled={!pdfFile}
                    aria-pressed={currentBoxType === boxType.id}
                    onMouseOver={() => { setHoveredBoxType(boxType.id) }}
                    onMouseOut={() => { setHoveredBoxType(null) }}
                    style={{
                      //borderColor: currentBoxType === boxType.id ? boxType.color : '#e0e0e0',
                      //backgroundColor: currentBoxType === boxType.id ? boxType.color : '#e0e0e0',
                      minWidth: boxType.minWidth,
                      color: getButtonTextColor(boxType),
                      backgroundColor: hoveredBoxType === boxType.id ? boxType.hoverBg : currentBoxType === boxType.id ? boxType.selectedBg : boxType.bgColor,
                      fontWeight: currentBoxType === boxType.id ? 'bold' : '',
                    }}
                  >
                    {boxType.label}
                  </button>
                ))}
              </div>
              {/* <button className={clsx(styles.btnCreateCustom,showCustomColumnCreator && styles.createCustomeActive)}
                onClick={handleCreateCustomClick}
              >
                CREATE CUSTOM
              </button> */}


            </div>

            {pdfFile && (<div className={styles.toolbarBtmSection}>


              {/* <div className={clsx(styles.zoomControls, "zoom-controls", styles.rotatePdfControl)} style={{ display: 'flex', alignItems: 'center' }}>
                <button
                  className={styles.rotateLeft}
                  onClick={() => { handlePageRotation((360 + pageRotations - 90) % 360); }}
                  title="Rotate Left"
                >
                  <RotateIcon />
                </button>
                <div className={styles.zoomPercentage}>
                  {pageRotations}
                </div>
                <button
                  className={styles.rotateRight}
                  onClick={() => { handlePageRotation((360 + pageRotations + 90) % 360); }}
                  title="Rotate Right"
                >
                  <RotateIcon />
                </button>
              </div> */}
               <div className={styles.pdfControlBg}>
                   <button className={styles.rotateLeft}
                   onClick={() => { 
                    //undo logic 
                    setDoUndo(true);
                   }}  
                   disabled={undoStack.length === 0}
                >
                  <RotateIcon1 />
                </button>
                 <button className={styles.rotateRight}
                  onClick={() => { 
                    //redo logic
                    setDoRedo(true);
                   }}
                   disabled={redoStack.length === 0}
                 >
                  <RotateIcon2 />
                </button>
               </div>
                 <div className={styles.orientationSliderBg}>
                    <OrientationSlider handlePageRotation={handlePageRotation} orientation={orientation} setOrientation={setOrientation} />
                  </div>

                   <Slider
                        min={0}
                        max={100}
                        value={gridOpacity*100}
                        onChange={(value)=>{setGridOpacity(value/100)}}
                        label="GRID"
                    />
              <div className={clsx(styles.zoomControls, "zoom-controls")} style={{ display: 'flex', alignItems: 'center' }}>
                <button
                  onClick={() => { handleZoomChange(Math.max(zoomPercentage - zoomChangePercent, minZoom)); }}
                  title="Zoom Out"
                  disabled={zoomPercentage <= 20}
                >
                  <ZoomOutIcon />
                </button>
                <div className={styles.zoomPercentage}>
                  {zoomPercentage}%
                </div>
                <button
                  onClick={() => { handleZoomChange(Math.min(zoomPercentage + zoomChangePercent, maxZoom)); }}
                  disabled={zoomPercentage >= 200}
                  title="Zoom In"
                >
                  <ZoomInIcon />
                </button>
              </div>

             
                 
            </div>

           

            )}
          </div>
          {/* <button
        onClick={() => {setDoExportCSV(true);}}
        disabled={false}
      >
        Export All Data to CSV
      </button> */}
          {/* <div style={{ marginLeft: '10px', display: 'inline-block' }}>
        <label style={{ display: 'block', marginBottom: '2px' }}>
          Grid Opacity: {gridOpacity.toFixed(2)}
        </label>
        <input
          type="range"
          min="0.01"
          max="1"
          step="0.01"
          value={gridOpacity}
          onChange={handleGridOpacityChange}
          style={{ width: '120px' }}
        />
      </div> 
      {isImageBasedPdf && <label style={{ marginLeft: '10px' }}>
        <input
          type="checkbox"
          checked={showMagnifyingGlass}
          onChange={handleShowMagnifyingGlassChange}
        />
        Show Magnifying Glass
      </label>}

      {isImageBasedPdf && <label style={{ marginLeft: '10px' }}>
        <input
          type="checkbox"
          checked={autoSelectColumns}
          onChange={handleAutoSelectColumnsChange}
        />
        Auto Select Columns
      </label>}
      {isImageBasedPdf && <label style={{ marginLeft: '30px' }}>
        Coverage %:
        <input
          type='number'
          min='1'
          max='100'
          step='1'
          value={overlapPercent}
          onChange={(e) => handleOverlapPercentChange(e.target.value)}
          style={{ width: '30px' }}
        />
      </label>}

      {notificationMessage && (
        <div style={{
          fontSize: '12px',
          color: '#FFcccc',
          fontWeight: 'bold'
        }}>
          {notificationMessage}
        </div>
      )}*/}

        </div>
      </div>


      {showCustomColumnCreator && (
        <div className="custom-column-creator-container">
          <CustomColumnCreator
            onSave={handleCustomColumnSave}
            onCancel={handleCustomColumnCancel}
          />
        </div>
      )}

      <VideoPopup
        open={showVideoPopup}
        onClose={() => setShowVideoPopup(false)}
        videoUrl="https://ik.imagekit.io/bryzos/video-library//staging/video-f073307e-cca8-487f-b4db-7184db01e1b5.mp4"
      />
    </div>
  );
};

export default Toolbar;
