import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import type { ICellEditorParams } from "ag-grid-community";

const NoSelectTextEditor = forwardRef((props: ICellEditorParams, ref) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [value, setValue] = useState(String(props.value ?? ""));

  // Expose methods AG Grid expects
  useImperativeHandle(ref, () => ({
    getValue: () => inputRef.current?.value, // returns current typed value
    afterGuiAttached: () => {
      const el = inputRef.current;
      if (!el) return;
      el.focus();
      const end = el.value.length;
      el.setSelectionRange(end, end); // caret at end
    },
  }));

  useEffect(() => {
    const el = inputRef.current;
    if (!el) return;
    el.focus();
    const end = el.value.length;
    el.setSelectionRange(end, end);

  }, []);

  return (
    <input
      ref={inputRef}
      value={value}
      onChange={(e) => {
        setValue(e.target.value);
        props.onValueChange(e.target.value);
      }
    } // keep state in sync
      className="ag-input-field-input ag-text-field-input"
      style={{ border: "solid green", background: "transparent", height: "100%" }}
      onKeyDown={(e) => {
        if (e.key === "Enter") {
          props.stopEditing();
        }
      }}
    />
  );
});

export default NoSelectTextEditor;
