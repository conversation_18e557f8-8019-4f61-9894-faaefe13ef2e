.savedBomPreviewContainer {
    width: 100%;
    height: 100%;
    padding-top: 24px;
    padding-right: 4px;

    .headerContainer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0px 35px 0px 40px;
        width: 100%;

        .headerItem {
            &:nth-child(1) {
                width: 194px;
            }

            &:nth-child(2) {
                width: 104px;
            }

            &:nth-child(3) {
                width: 38px;
            }

            &:nth-child(4) {
                width: 141px;
                text-align: right;
            }

            font-family: Inter;
            font-size: 15px;
            font-weight: normal;
            letter-spacing: 0.6px;
            text-align: left;
            color: #459fff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

}

.createPOContainer {
    // overflow-y: auto;
    height: 100%;
    max-height: calc(100% - 40px);
    position: relative;
    scroll-behavior: smooth;


    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
}

.createPOContainer {
    position: relative;
    height: 100%;
    overflow: auto;
}
.addPoLineTable {
    position: relative;
    max-height: calc(100% - 40px);
    overflow-y: scroll;
    z-index: 3;
    width: 100%;
    width: 100%;
    border-spacing: 1px;
    border-collapse: collapse;

    &::-webkit-scrollbar {
        width: 8px;
        height: 6px;
    }

    thead {
        width: 100%;
        position: sticky;
        top: 0px;
        z-index: 100;
        background: url(../../assets/New-images/AppBG.svg) no-repeat;
    }
    tr {
        th {
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: normal;
            text-align: left;
            color: #9b9eac;
            padding-top: 24px;
            padding-bottom: 16px;
            &:nth-child(1) {
                padding-left: 40px;
            }
            &:nth-child(3), &:nth-child(4){
                 text-align: center;
            }
            
            &:nth-child(5) {
                text-align: right;
                 padding-right: 24px;
            }
            
        }
        td {
            vertical-align: top;
            padding-bottom: 32px;
            &:nth-child(1) {
                padding-left: 36px;
                padding-right: 18px;
            }

             &:nth-child(3), &:nth-child(4){
                 text-align: center;
            }
            
            &:nth-child(5) {
                text-align: right;
                padding-right: 24px;
            }
         
            
        }
    }
}

.addPoLineTable {
    will-change: transform;
}
.poDescription {
    background-color: transparent;
    border: 0;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.43;
    letter-spacing: 1.4px;
    text-align: left;
    color: #fff;
}

.priceAndUnit {
    font-family: Inter;
    font-size: 14px;
    text-align: center;
    color: #fff;
    display: flex;
    justify-content: center;
    gap: 8px;
    position: relative;
    .selectUom,.selectUom1{
        position: absolute;
        right: 0;
    }
}
.extendedValue {
    font-family: Inter;
    font-size: 14px;
    text-align: right;
    color: #fff;
    display: flex;
    justify-content: right;
  }
.prodId {
    display: flex;
    align-items: center;
    position: relative;
    flex-direction: column;

    .lineNumberContainer {
        display: flex;
        flex-direction: column;
        gap: 5px;
        align-items: center;
        justify-content: center;
        text-align: center;

        .hiddenCheckbox {
            display: none;
        }

        .customNumberToggle {
            background-image: url(../../assets/New-images/Product-Id-BG.svg);
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            border-radius: 5px;
            border: solid 0.5px rgba(255, 255, 255, 0.16);
            width: 30px;
            height: 30px;
            font-family: Syncopate;
            font-size: 16px;
            font-weight: bold;
            font-stretch: normal;
            font-style: normal;
            line-height: 1;
            letter-spacing: 1.12px;
            text-align: left;
            color: #9b9eac;
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 3px;
            cursor: pointer;
            transition: all 0.2s ease;

            &.active {
                background-image: url(../../assets/New-images/Usa-Only-Without-Text.svg);
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                color: #0f0f14;
                border: none;
                border-radius: 2px;
            }
        }

        .usaOnlyText {
            font-family: Syncopate;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
            font-weight: normal;

            &.visibilityHidden {
               display: none;
            }
        }
    }
}
.skippedLineContent {
    display: flex;
    gap: 16px;
    font-family: Inter;
    font-size: 14px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.98px;
    color: #fff;

    .skippedLineActionBtns {
        display: flex;
        gap: 8px;

        .btn {
            width: 114px;
            height: 20px;
            border-radius: 100px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.5;
            letter-spacing: -0.13px;
            text-align: center;
            color: rgba(255, 255, 255, 0.6);
        }
    }
}

.removeLineIconContainer{
    display: flex;
    justify-content: center;
    align-items: center;
    .removeLineIcon{
        // position: absolute;
        margin-top: 6px;
    }
}