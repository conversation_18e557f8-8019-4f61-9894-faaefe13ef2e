import { routes, useCreatePoStore, useGlobalStore, useSearchStore, useSellerOrderStore } from "@bryzos/giss-ui-library";
import Home from '../search/home';
import CreatePo from "../buyer/CreatePo";
import OrderManagement from "../OrderManagement";
import { useEffect, useRef } from "react";
import styles from './searchResult.module.scss';
import { userRole } from "src/renderer2/common";
import SellerOrderViewingPane from "../buyer/sellerOrderViewing/SellerOrderViewingPane";
import { useGlobalSearchStore } from "../GlobalSearchField/globalSearchStore";

const SearchResult = () => {
    const fullWidthContainerWithoutRouteTabRef = useRef(null);
    const userData = useGlobalStore(state => state.userData);
    const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
    const isSeller = userData?.data?.type === userRole.sellerUser;
    const selectedQuote = useCreatePoStore((state) => state.selectedQuote);
    const orderDetail = useSellerOrderStore(state => state.orderToBeShownInOrderAccept);
    const setSelectedObject = useGlobalSearchStore(state => state.setSelectedObject);

    useEffect(() => {
        return () => {
            setSelectedObject(null);
        }
    }, [])

    const nothingToShow = !(selectedQuote || orderDetail || selectedSavedSearch);

    if(nothingToShow){
        return (
        <div className={styles.root}>
           <div className={styles.emptyStateMessage}>
           <h3 className={styles.emptyStateTitle}>
                Search Results
            </h3>
            <p> This is the viewing pane.
          <br /> Select from the list on the left to
          <br /> review searched item.</p>
           </div>
        </div>
        )
    }

    return (
        <div className={styles.root} style={{height:'100%', position:'relative'}}>
            {isSeller ?<> 
                {orderDetail && <SellerOrderViewingPane containerRef={fullWidthContainerWithoutRouteTabRef} /> }
                {selectedQuote && <OrderManagement />}
            </>: null }
            {!isSeller ? <>
                {selectedSavedSearch && <Home />}
                {selectedQuote && ((selectedQuote.order_type === 'QUOTE' || selectedQuote.order_type === 'PO')? <CreatePo componentType={selectedQuote.order_type} />: <OrderManagement />)}
            </> : null}
            
        </div>
    );
};

export default SearchResult;