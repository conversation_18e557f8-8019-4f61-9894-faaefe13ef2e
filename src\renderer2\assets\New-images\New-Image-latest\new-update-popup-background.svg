<svg width="560" height="572" viewBox="0 0 560 572" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_101_1195)">
<rect width="560" height="572" rx="20" fill="#191A20"/>
<g filter="url(#filter0_f_101_1195)">
<circle cx="100" cy="-100" r="100" fill="#9786FF"/>
<circle cx="100" cy="-100" r="100" fill="url(#paint0_linear_101_1195)" fill-opacity="0.8"/>
<g clip-path="url(#paint1_angular_101_1195_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.1 0.130964 -0.0937249 0.182974 100 -91.8782)"><foreignObject x="-939.022" y="-939.022" width="1878.04" height="1878.04"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(254, 254, 254, 1) 0deg,rgba(255, 255, 255, 0) 66.6644deg,rgba(255, 255, 255, 1) 143.541deg,rgba(255, 255, 255, 0) 199.792deg,rgba(255, 255, 255, 1) 272.918deg,rgba(255, 255, 255, 0) 349.795deg,rgba(254, 254, 254, 1) 360deg);height:100%;width:100%;opacity:0.5"></div></foreignObject></g></g><circle cx="100" cy="-100" r="100" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.99964576959609985,&#34;g&#34;:0.99964576959609985,&#34;b&#34;:0.99964576959609985,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.18517892062664032},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.39872443675994873},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.55497723817825317},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.75810593366622925},{&#34;color&#34;:{&#34;r&#34;:1.0,&#34;g&#34;:1.0,&#34;b&#34;:1.0,&#34;a&#34;:0.0},&#34;position&#34;:0.97165143489837646}],&#34;stopsVar&#34;:[],&#34;transform&#34;:{&#34;m00&#34;:199.99996948242188,&#34;m01&#34;:-187.44989013671875,&#34;m02&#34;:93.724960327148438,&#34;m10&#34;:261.92892456054688,&#34;m11&#34;:365.94882202148438,&#34;m12&#34;:-405.81704711914062},&#34;opacity&#34;:0.50,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
</g>
<rect x="0.5" y="0.5" width="559" height="571" rx="19.5" stroke="url(#paint2_radial_101_1195)" stroke-opacity="0.4"/>
<defs>
<filter id="filter0_f_101_1195" x="-100" y="-300" width="400" height="400" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_101_1195"/>
</filter>
<clipPath id="paint1_angular_101_1195_clip_path"><circle cx="100" cy="-100" r="100"/></clipPath><linearGradient id="paint0_linear_101_1195" x1="100" y1="-139.594" x2="235.749" y2="-65.4773" gradientUnits="userSpaceOnUse">
<stop stop-color="#E7ECEF"/>
<stop offset="1" stop-color="#E7ECEF" stop-opacity="0"/>
</linearGradient>
<radialGradient id="paint2_radial_101_1195" cx="0" cy="0" r="1" gradientTransform="matrix(-57.8667 137.347 -133.9 -28.6493 173.133 -9.64419)" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_101_1195">
<rect width="560" height="572" rx="20" fill="white"/>
</clipPath>
</defs>
</svg>
