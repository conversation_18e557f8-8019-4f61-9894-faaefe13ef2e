import React, { useState } from 'react';
import styles from './ItemAttentionNav.module.scss';
import { ReactComponent as PrevIcon } from '../../../../assets/New-images/New-Image-latest/prevOrderIcon.svg';
import { ReactComponent as NextIcon } from '../../../../assets/New-images/New-Image-latest/nextOrderIcon.svg';
import clsx from 'clsx';

interface AttentionItem {
  id: string;
  type: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

interface ItemAttentionNavProps {
  attentionItems: AttentionItem[];
  handleNext: () => void;
  handlePrev: () => void;
  currentFocusedItemIndex: number;
  totalCount: number;
}

const ItemAttentionNav: React.FC<ItemAttentionNavProps> = ({
  attentionItems,
  currentFocusedItemIndex,
  handleNext,
  handlePrev, 
  totalCount
}) => {

  const onNext = () => {
      handleNext();
  };

  const onPrev = () => {
      handlePrev();
    }


  return (
    <div className={styles.itemAttentionNav}>
      <div className={styles.navigation}>
        <button className={clsx(styles.navButton,styles.prevButton)}
          onClick={onPrev} disabled={currentFocusedItemIndex === 0 || totalCount === 0} aria-label="Previous item">
          <PrevIcon />
        </button>

      <div className={styles.counter}>
        <span className={styles.total}>{totalCount}</span><span>Items Needing Your Attention</span>
      </div>
        <button className={clsx(styles.navButton,styles.nextButton)} onClick={onNext}
          disabled={currentFocusedItemIndex === attentionItems.length - 1 || totalCount === 0} aria-label="Next item"
        >
          <NextIcon />
        </button>
      </div>
    </div>
  );
};

export default ItemAttentionNav;
