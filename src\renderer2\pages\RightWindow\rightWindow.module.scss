
.rightWindowContainer{
    display: flex;
    flex-direction: column;
    flex: 1;
    // margin-left: 24px;
    position: relative;
    align-items: center;
    overflow-y: scroll;
    padding-right: 10px;
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    .rightWindow {
        position: relative;
        width: 100%;
        display: flex;
        flex-direction: column;
        height: 100%;
        .wrapperOverlay{
          left: 0px !important; 
          border-bottom-right-radius: 16px;
          border-top-left-radius: 0px;
          border-bottom-left-radius: 0px;
        }
      } 
      .nonFrameContainer{
        flex: 1;
        overflow: hidden;
        position: relative;
        top: 24px;
        .roundCorner{
          svg{
            position: relative;
            top: -17px;
            left: -13px;
            path{
              fill-opacity: unset;
              fill: rgb(135 135 135);
            }
          }
        }
      }
      .videoContainer{
        width: 322px;
        height: 450px;
        flex-grow: 0;
        margin: 30px 0 0 0px;
        border-radius: 20px;
        border-style: solid;
        border-width: 1px;
        border-color: transparent;
        border-image-slice: 1;
        background-image: linear-gradient(340deg, #2b2d33 200%, #0f0f14 -67%);        
        background-origin: border-box;
        background-clip: content-box, border-box;
        overflow: hidden;
        position: relative;
        .closeButton{
            position: absolute;
            top: 9px;
            right: 13px;
            width: auto;
            color: #fff;
            font-size: 20px;
            cursor: pointer;
            z-index: 100;
        }
      }
}
