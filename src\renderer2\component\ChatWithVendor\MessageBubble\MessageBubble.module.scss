.messageBubble {
    padding: 8px 12px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.08);
     color: #dbdcde;
    max-width: 70%;
    font-family: Inter;
    font-size: 12px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: normal;
    text-align: left;
    word-wrap: break-word;
    min-width: 30px;

    a {
        color: inherit;
        text-decoration: none;
        cursor: pointer;

        &:hover {
            text-decoration: underline;
        }
    }
}

.othersMessage {
    background-color: #9b9eac;
    color: #0f0f14;
}

.adminRole{
  background-color: #4285F4;
  color: #fff;
}

:global(.MuiTooltip-popper) {
    .linkTooltip {
        background: #181e2b;
        color: #fff;
        border-radius: 8px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.22);
        padding: 10px;
        max-width: 200px;
        min-width: 200px;
    }
}