import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { reactQueryKeys } from "../common";
import { useGlobalStore, userRole } from "@bryzos/giss-ui-library";

const useGetOrderLines = () => {
  const { setShowLoader } = useGlobalStore();
  return useMutation(async (id?: string) => {
    try {
      const userData = useGlobalStore.getState().userData;
      const isSeller = userData?.data?.type === userRole.sellerUser;
      const API_URL = isSeller ? import.meta.env.VITE_API_ORDER_SERVICE + "/seller/claim-order/" + id : import.meta.env.VITE_API_ORDER_SERVICE + "/buyer/order/" + id;
      const response = await axios.get(API_URL);

      // if (response.data?.data?.error_message) {
      //   return [];
      // }

      return response.data;
    } catch (error: any) {
      setShowLoader(false);
      throw new Error(error?.message || "Failed to fetch Search data");
    }
  });
};

export default useGetOrderLines; 