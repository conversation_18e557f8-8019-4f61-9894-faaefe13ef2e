import { commomKeys, dateTimeFormat, InstantPurchasingWrapper, mobileDiaglogConst, noIdGeneric, orderType, useBuyerSettingStore, useCreatePoStore, useGetDeliveryDate, useGlobalStore, useOrderManagementStore, userRole, useStateZipValidation } from '@bryzos/giss-ui-library';
import React, { useEffect, useMemo, useRef, useState, forwardRef, useImperativeHandle } from 'react'
import { useLocation, useNavigate } from 'react-router-dom';
import { disputeCounterStatus, localStorageKeys, navigationConfirmMessages, routes, undo } from 'src/renderer2/common';
import clsx from 'clsx';
import InputWrapper from 'src/renderer2/component/InputWrapper';
import CustomTextField from 'src/renderer2/component/CustomTextField';
import Calendar from 'src/renderer2/component/Calendar/Calendar';
import { Autocomplete, ClickAwayListener, Dialog, Fade, IconButton, Popover, TextField, Tooltip } from '@mui/material';
import { Controller } from 'react-hook-form';
import StateDropDown from 'src/renderer2/component/StateDropDown/StateDropDown';
import { ReactComponent as ChooseOneIcon } from 'src/renderer2/assets/New-images/Choose-One.svg';
import { ReactComponent as UploadBOMIcon } from 'src/renderer2/assets/New-images/Upload-BOM.svg';
import { ReactComponent as UploadBOMIconHover } from 'src/renderer2/assets/New-images/Bom-Upload-Hover.svg';
import { ReactComponent as Shape1 } from 'src/renderer2/assets/New-images/New-Image-latest/shape1.svg';
import { ReactComponent as Shape2 } from 'src/renderer2/assets/New-images/New-Image-latest/shape2.svg';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';
import dayjs from 'dayjs';
import { useGenericForm } from 'src/renderer2/hooks/useGenericForm';
import { poHeaderSchema } from 'src/renderer2/models/poHeader.model';
import useMutateGetDeliveryAddress from 'src/renderer2/hooks/useMutateGetDeliveryAddress';
import { formatDisplayDateForTemplate, formatDisputePayload, getLocal, setLocal } from 'src/renderer2/helper';
import usePostPoUpdatePricing from 'src/renderer2/hooks/usePostPoUpdatePricing';
import ShipmentsTab from 'src/renderer2/pages/buyer/newSettings/tabs/ShipmentsTab';
import styles from './SellerHeaderInfo.module.scss';
import { ReactComponent as PointRightIcon } from '../../../../assets/New-images/PointRight.svg';
import { ReactComponent as UndoIcon } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/icon-reset1.svg';
interface SellerHeaderInfoRef {
    watch: any;
    setValue: any;
}

const SellerHeaderInfo = forwardRef<SellerHeaderInfoRef, any>((
    {
        formInputGroupRef,
        setOpenErrorDialog,
        setErrorMessage,
        saveBomHeaderDetails,
        disableBidBuyNow,
        setOpenDeliveryToDialog,
        openDeliveryToDialog,
        scrollToTop,
        isSavedBom,
        focusJobPoInput,
        setCameFromSavedBom,
        saveUserActivity,
        HeaderDetailsConfirmedRef,
        isHeaderDetailsConfirmed,
        setIsHeaderDetailsConfirmed,
        cameFromSavedBom,
        handleStoreUndoStack,
        isOrderLineChanges,
        setIsStateZipValChange,
        componentType,
        currentFocusedItem,
        setFinalDisputePayload,
        finalDisputePayload,
        removeFromAttentionItems,
        addToAttentionItems
    }, ref) => {
    const navigate = useNavigate();
    const location = useLocation();
    const { setShowLoader, backNavigation, setCreatePoSessionId, resetHeaderConfig, userData, setBackNavigation, bryzosPayApprovalStatus, setBryzosPayApprovalStatus, referenceData, productData, productMapping, discountData, referenceDataUpdated } = useGlobalStore();
    const { bomProductMappingSocketData, setBomProductMappingSocketData, isCreatePOModule, setIsCreatePOModule, setIsCreatePoDirty, isCreatePoDirty, setCreatePoData, createPoData, bomProductMappingDataFromSavedBom, createPoDataFromSavedBom, setCreatePoDataFromSavedBom, setBomProductMappingDataFromSavedBom, setBomDataIdToRefresh, bomDataIdToRefresh, bomSummaryViewFilter, setBomSummaryViewFilter, setUploadBomInitialData, uploadBomInitialData } = useCreatePoStore();
    const orderInfoIsFilled = useCreatePoStore((state: any) => state.orderInfoIsFilled);
    const setOrderInfoIsFilled = useCreatePoStore((state: any) => state.setOrderInfoIsFilled);
    const selectedQuote = useCreatePoStore((state: any) => state.selectedQuote);
    const setSelectedQuote = useCreatePoStore((state: any) => state.setSelectedQuote);
    const quoteList = useCreatePoStore((state: any) => state.quoteList);
    const setQuoteList = useCreatePoStore((state: any) => state.setQuoteList);
    const purchasingList = useCreatePoStore((state: any) => state.purchasingList);
    const setPurchasingList = useCreatePoStore((state: any) => state.setPurchasingList);
    const isEditingPo = useOrderManagementStore((state: any) => state.isEditingPo);
    const setIsEditingPo = useOrderManagementStore((state: any) => state.setIsEditingPo);
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const { setOpenLeftPanel, setDisplayLeftPanel } = useLeftPanelStore();
    const setUpdatedDraftId = useCreatePoStore((state: any) => state.setUpdatedDraftId);
    const containerRef = useRef(null);
    const fileInputRef = useRef(null); // Add ref for file input
    const [isFocused, setIsFocused] = useState(false);
    const [stateInputFocus, setStateInputFocus] = useState(false);
    const [autocompleteOpen, setAutocompleteOpen] = useState(false);
    const [autocompleteOpenLine2, setAutocompleteOpenLine2] = useState(false);
    const [autocompleteOpenCity, setAutocompleteOpenCity] = useState(false);
    const { mutateAsync: getDeliveryAddresses, data: deliveryAddressData } = useMutateGetDeliveryAddress();
    const [stateDropDownValue, setStateDropDownValue] = useState<any>('');
    const { buyerSetting } = useBuyerSettingStore();
    const getDeliveryDate = useGetDeliveryDate();
    const [deliveryDateMap, setDeliveryDateMap] = useState({});
    const [deliveryDates, setDeliveryDates] = useState([]);
    const [disableDeliveryDate, setDisableDeliveryDate] = useState(false);
    const [states, setStates] = useState([]);
    const [addressDialogOpen, setAddressDialogOpen] = useState(false);
    const [initialPoData, setInitialPoData] = useState({});
    const jobPoInputRef = useRef(null);
    const [undoStackObject, setUndoStackObject] = useState({});
    const [isDataChanged, setIsDataChanged] = useState(false);
    const [autoFocusInDeliveryAddress, setAutoFocusInDeliveryAddress] = useState(false);
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const [calendarAnchorEl, setCalendarAnchorEl] = useState<HTMLButtonElement | null>(null);

    const {
        control,
        register,
        handleSubmit,
        getValues,
        setValue,
        setError,
        watch,
        clearErrors,
        reset,
        trigger,
        errors,
        isValid
    } = useGenericForm(poHeaderSchema);

    const checkStateZipValidation = useStateZipValidation();
    const { mutateAsync: updatePricing } = usePostPoUpdatePricing();
    const isSeller = userData?.data?.type === userRole.sellerUser;
    const isOrderManagementPage = componentType === 'ORDER' || location.pathname === routes.orderManagementPage;
    const isQuotePage = componentType === 'QUOTE' || location.pathname === routes.quotePage;
    const isPurchaseOrderPage = componentType === 'PO' || location.pathname === routes.createPoPage;
    const initialDeliveryByCounterData = initialPoData?.order_level_dispute?.deliver_by?.length > 0 ? initialPoData?.order_level_dispute?.deliver_by[0] : {};
    const initialDeliveryToCounterData = initialPoData?.order_level_dispute?.deliver_to?.length > 0 ? initialPoData?.order_level_dispute?.deliver_to[0] : {};
    const lastDeliverByCounterData = initialPoData?.order_level_dispute?.deliver_by?.length > 0 ? initialPoData?.order_level_dispute?.deliver_by[initialPoData?.order_level_dispute?.deliver_by.length - 1] : {};
    const lastDeliverToCounterData = initialPoData?.order_level_dispute?.deliver_to?.length > 0 ? initialPoData?.order_level_dispute?.deliver_to[initialPoData?.order_level_dispute?.deliver_to.length - 1] : {};
    const updateFinalPayload = {...finalDisputePayload};
    const lastCounterFromSeller = lastDeliverByCounterData?.created_by === userRole.sellerUser;
    const lastCounterFromBuyer = lastDeliverByCounterData?.created_by === userRole.buyerUser;
    const calendarAnchorRef = useRef<HTMLSpanElement>(null);
    const deliverByContainerRef = useRef(null);
    const inPayloadDeliveryByCounterPresent = finalDisputePayload?.order_level?.deliver_by && finalDisputePayload?.order_level?.deliver_by?.action === 'counter';

    useEffect(() => {
                if (deliverByContainerRef.current && initialPoData?.order_level_dispute?.deliver_by?.length > 0) {
                    deliverByContainerRef.current.scrollTop = deliverByContainerRef.current.scrollHeight;
                }
            }, [initialPoData?.order_level_dispute?.deliver_by]);

    useEffect(() => {
        setOrderInfoIsFilled(
            !!watch('shipping_details.line1') &&
            !!watch('shipping_details.city') &&
            !!watch('shipping_details.state_id') &&
            !!watch('shipping_details.zip') &&
            !!watch('buyer_internal_po') &&
            (watch('order_type') === orderType.QUOTE || !!watch('delivery_date')) &&
            !errors?.shipping_details?.zip?.message
        )
    }, [
        watch('shipping_details.line1'),
        watch('shipping_details.city'),
        watch('shipping_details.state_id'),
        watch('shipping_details.zip'),
        watch('buyer_internal_po'),
        watch('order_type'),
        watch('delivery_date'),
        errors?.shipping_details?.zip?.message
    ]);

    const getDeliveryDateData = async (deliveryAddressId: string | null) => {
        try {
            const selectedQuote = useCreatePoStore.getState().selectedQuote;
            // if(!deliveryAddressId){
            //     showCommonDialog(null, "Please select a delivery address", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
            //     return []
            // }
            let deliveryDateList = [];

            const payload = {
                id: deliveryAddressId,
                timestamp: selectedQuote && dayjs(selectedQuote?.created_date).format(dateTimeFormat.isoDateTimeWithTFormat)
            }
            const res = await getDeliveryDate.mutateAsync(payload);
            if (res?.data?.data) {
                deliveryDateList = res.data.data
            }
            const optionMap = deliveryDateList.reduce((acc, option) => {
                acc[option.value] = option;
                return acc;
            }, {});
            setDeliveryDateMap(optionMap)
            setDeliveryDates(deliveryDateList);
            let disableDeliveryDates = true;
            deliveryDateList.forEach(deliveryDateObj => {
                // if(deliveryDateObj.days_to_add === deliveryDaysAddValue){
                //     const deliveryDate = !deliveryDateObj.disabled ? deliveryDateObj.value : null;
                //     setValue('delivery_date', dayjs(deliveryDate).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit));
                // }
                if (!deliveryDateObj.disabled && disableDeliveryDates) {
                    disableDeliveryDates = false;
                }
            });
            setDisableDeliveryDate(disableDeliveryDates)
            return optionMap
        } catch (error) {
            console.error("err @>>>>>>>", error)
            return []
        }
    }

    const handleUndoForHeader = (lastElement: any) => {
        if (lastElement?.multipleData) {
            Object.keys(lastElement.multipleData).forEach((key: any) => {
                setValue(key, lastElement.multipleData[key]);
            });
        } else {
            setValue(lastElement.name, lastElement.value);
            if (lastElement.name.includes("shipping_details")) {
                setAutoFocusInDeliveryAddress(true);
                handleDeliveryToClick();
            }
        }
        if (lastElement.id === "shipping_details.state_id") {
            setStateDropDownValue(lastElement.multipleData['shipping_details.state_code']);
        }
        setIsCreatePoDirty(true);
        saveBomHeaderDetails();
        setTimeout(() => {
            const ele = document.getElementById(`${lastElement.id}`);
            if (ele) {
                ele.focus();
            }
        }, 200)
    }

    // Expose orderInfoIsFilled to parent component
    useImperativeHandle(ref, () => ({
        initializePoHeaderForm,
        getHeaderFormData,
        watch,
        setValue,
        handleUndoForHeader
    }), [watch, setValue]);

    useEffect(() => {
        if (focusJobPoInput) {
            setTimeout(() => {
                jobPoInputRef?.current?.focus();
            }, 100)
        }
    }, [focusJobPoInput])

    useEffect(() => {
        if (referenceData) {
            setStates(referenceData?.ref_states)
        }
    }, [referenceData])

    useEffect(() => {
        setValue('isEdit', true);
        if (!isSeller) {
            getDeliveryAddresses();
        }
        function handleClickOutside(event) {
            if (containerRef.current && !containerRef.current.contains(event.target)) {
                setIsFocused(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
            const orderTypeData = isPurchaseOrderPage ? orderType.PO : orderType.QUOTE;
            reset();
            setValue('buyer_internal_po', '');
            setValue('delivery_date', '');
            setValue('shipping_details', {});
            setValue('order_type', isOrderManagementPage ? '' : orderTypeData);
            setValue('delivery_date_offset', '');
            setValue('isEdit', true);
            setValue('id', undefined);
            setStateDropDownValue('');
        };
    }, []);

    useEffect(() => {
        if (watch('shipping_details.zip') && watch('shipping_details.state_id') && !isSeller) {
            onStateZipChange()
        }
    }, [watch('shipping_details.zip'), watch('shipping_details.state_id')])

    useEffect(() => {
        if (buyerSetting) setRecevingHoursAndDeliveryDateOffset()
    }, [buyerSetting])

    useEffect(() => {
        if (!selectedQuote || selectedQuote?.length === 0) {
            const orderTypeData = isPurchaseOrderPage ? orderType.PO : orderType.QUOTE;
            reset();
            setValue('buyer_internal_po', '');
            setValue('delivery_date', '');
            setValue('shipping_details', {});
            setValue('order_type', isOrderManagementPage ? '' : orderTypeData);
            setValue('delivery_date_offset', '');
            setValue('isEdit', true);
            setValue('id', undefined);
            setStateDropDownValue('');
        }
    }, [selectedQuote])

    useEffect(() => {
        if (isEditingPo) {
            setValue('isEdit', isEditingPo);
        }
    }, [isEditingPo])

    const initializePoHeaderForm = async (initialData: any) => {
        setInitialPoData(initialData);
        const deliveryAddressDataFromSetting = buyerSetting?.delivery_address || [];
        // if(deliveryAddressDataFromSetting?.length === 0 || deliveryAddressDataFromSetting?.user_delivery_receiving_availability_details?.length === 0) return;
        const defaultDeliveryAddress = deliveryAddressDataFromSetting?.find((address: any) => address.is_default) || deliveryAddressDataFromSetting[0];


        const deliveryMap = isSeller ? {} : await getDeliveryDateData(defaultDeliveryAddress?.id || undefined);
        if (initialData && 'isEdit' in initialData) {
            setValue('isEdit', initialData?.isEdit);
        } else {
            setValue('isEdit', true);
        }
        if (initialData) {
            if (initialData?.cameFromQuote) {
                setCameFromSavedBom(true);
            }
            setValue('id', initialData.id);
            setValue('buyer_internal_po', initialData.buyer_internal_po);
            if ('pricing_expired' in initialData) {
                setValue('pricing_expired', initialData.pricing_expired);
            }
            setValue('shipping_details', initialData.shipping_details);
            setValue('shipping_details.delivery_address_id', defaultDeliveryAddress?.id);
            setValue('delivery_date', initialData.delivery_date);
            setValue('order_type', initialData.order_type);
            if (initialData?.delivery_date_offset) {
                setValue('delivery_date_offset', initialData.delivery_date_offset)
            } else {
                const selectedDate = dayjs(initialData.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
                setValue('delivery_date_offset', deliveryMap?.[selectedDate]?.days_to_add);
            }
            setStateDropDownValue(referenceData?.ref_states?.find((state: any) => state.id === initialData?.shipping_details?.state_id)?.code || '')
        }
    }

    const onStateZipChange = async () => {
        await handleStateZipValidation();
    }

    const setRecevingHoursAndDeliveryDateOffset = () => {
        const deliveryAddressDataFromSetting = buyerSetting?.delivery_address || [];
        // if(deliveryAddressDataFromSetting?.length === 0 || deliveryAddressDataFromSetting?.user_delivery_receiving_availability_details?.length === 0){
        //     setAddressDialogOpen(true);
        //     return;
        // }
        const defaultDeliveryAddress = deliveryAddressDataFromSetting?.find((address: any) => address.is_default) || deliveryAddressDataFromSetting[0];
        // getDeliveryDateData(defaultDeliveryAddress?.id || undefined);
        const deliveryReceivingHours = defaultDeliveryAddress?.user_delivery_receiving_availability_details;
        if (deliveryReceivingHours?.length !== 0) {
            setValue('recevingHours', deliveryReceivingHours)
        }
        const deliveryDaysAddValue = defaultDeliveryAddress?.delivery_days_add_value ?? referenceData?.ref_delivery_date[0]?.days_to_add;
        setValue('delivery_date_offset', deliveryDaysAddValue);
    }

    const getHeaderFormData = () => {
        return {
            buyer_internal_po: watch('buyer_internal_po'),
            shipping_details: watch('shipping_details'),
            delivery_date: watch('delivery_date'),
            order_type: watch('order_type')
        }
    }

    const handleStateZipValidation = async (): Promise<Boolean> => {
        const zipCode = watch("shipping_details.zip");
        const stateCode = watch("shipping_details.state_id");
        let isValid: Boolean = false;
        if (zipCode) {
            setValue('shipping_details.validating_state_id_zip', true);
            if (zipCode.length > 4 && stateCode) {
                const checkStateZipResponse: any = await checkStateZipValidation.mutateAsync({ zipCode, stateCode: Number(stateCode) });
                if (checkStateZipResponse.data.data === true) {
                    clearErrors(["shipping_details.zip", "shipping_details.state_id"]);
                    isValid = true;
                } else {
                    showStateZipError();
                }
            } else {
                setError("shipping_details.zip", { message: "Zip Code is not valid" });
            }
            setValue('shipping_details.validating_state_id_zip', false);
        }
        return isValid;
    };

    const showStateZipError = () => {
        const zipCode = "shipping_details.zip";
        const stateCode = "shipping_details.state_id";
        setError(stateCode, { message: "The State and Zipcode do not match." });
        setError(zipCode, { message: "The State and Zipcode do not match." });
    }



    const allowedDates = useMemo(() => {
        return deliveryDates
            .filter(date => !date?.disabled)
            .map(date => new Date(date?.value));
    }, [deliveryDates]);

    const handleDateSelect = (date: any) => {
        const selectedDate = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
        setValue('delivery_date_offset', deliveryDateMap?.[selectedDate]?.days_to_add);
        setIsCreatePoDirty(true)
        handleStoreUndoStack({ ...undoStackObject, currentValue: selectedDate, uniqueId: "delivery_date" });
    }

    const handleDeliveryToClick = () => {
        if (!watch('isEdit')) {
            return;
        }
        setOpenDeliveryToDialog(true);
        setIsFocused(true);
    }

    const formatShippingAddress = (data: any): string => {
        const shipping = data || {};
      
        const line1 = shipping?.line1?.trim();
        const line2 = shipping?.line2?.trim();
        const city = shipping?.city?.trim();
        const state = shipping?.state_code?.trim();
        const zip = shipping?.zip?.trim();
      
        // Build address parts conditionally
        const parts: string[] = [];
      
        if (line1) parts.push(line1);
        if (line2) parts.push(line2);
        if (city) parts.push(city);
        
        // Combine state and zip together (with a space if both exist)
        const stateZip = [state, zip].filter(Boolean).join(' ');
        if (stateZip) parts.push(stateZip);
      
        // If all parts are missing, return '-'
        return parts.length > 0 ? parts.join(', ') : '-';
      };
      
      const handleOpenCalendar = () => {
        setIsCalendarOpen(true);
    };

    console.log('initialPoData', initialPoData);

    const handleClickAway = (date) => {
        const lastCounterData = {...initialPoData?.order_level_dispute?.deliver_by[initialPoData?.order_level_dispute?.deliver_by.length - 1]};
        const updateInitialPoData = {...initialPoData};
        if(!lastCounterData?.new_counter){
            console.log(">>>>> lastCounterData", lastCounterData);
            updateInitialPoData.order_level_dispute.original_deliver_by_dispute = [...initialPoData.order_level_dispute.deliver_by];
        }
        lastCounterData.delivery_date = dayjs(date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit);
        lastCounterData.created_by = userData?.data?.type;
        lastCounterData.new_counter = true;
        updateInitialPoData.order_level_dispute.deliver_by.push(lastCounterData);
        setSelectedQuote(updateInitialPoData);
        setInitialPoData(updateInitialPoData);

        const payload = formatDisputePayload("orderLevel", "deliver_by", {
            delivery_date: lastCounterData?.delivery_date,
            counter_id: lastCounterData?.counter_id,
        }, "counter");
        console.log('payload', payload);
        updateFinalPayload.order_level = { ...updateFinalPayload.order_level, ...payload.order_level };
        removeFromAttentionItems("deliver_by", "order_level");
        setFinalDisputePayload({ ...updateFinalPayload });
        setIsCalendarOpen(false);
    };

    const handleCounterClick = () => {
        removeFromAttentionItems("deliver_by", "order_level");
        setIsCalendarOpen(true);
    };

    const handleAcceptCounter = () => {
        console.log('handleAcceptCounter');
        const payload = formatDisputePayload("orderLevel", "deliver_by", {
            delivery_date: dayjs(lastDeliverByCounterData?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
            counter_id: lastDeliverByCounterData?.counter_id,
        }, "accept");
        console.log('payload', payload);
        updateFinalPayload.order_level = {...updateFinalPayload.order_level, ...payload.order_level};
        removeFromAttentionItems("deliver_by", "order_level");
        setFinalDisputePayload({...updateFinalPayload});
        setIsCalendarOpen(false);
    };

    const handleRejectCounter = () => {
        const payload = formatDisputePayload("orderLevel", "deliver_by", {
            delivery_date: dayjs(initialDeliveryByCounterData?.delivery_date).format(dateTimeFormat.dateSeparateWithSlashAndDaySingleDigit),
            counter_id: initialDeliveryByCounterData?.counter_id,
        }, "reject");
        console.log('payload', payload);
        updateFinalPayload.order_level = {...updateFinalPayload.order_level, ...payload.order_level};
        removeFromAttentionItems("deliver_by", "order_level");
        setFinalDisputePayload({...updateFinalPayload});
    };

    const handleAcceptDeliveryToCounter = () => {
        const payload = formatDisputePayload("orderLevel", "deliver_to", {
            line1: lastDeliverToCounterData?.line1,
            line2: lastDeliverToCounterData?.line2?.trim() || null,
            city: lastDeliverToCounterData?.city,
            state_id: lastDeliverToCounterData?.state_id,
            zip: lastDeliverToCounterData?.zip,
            counter_id: lastDeliverToCounterData?.counter_id,
        }, "accept");
        console.log('payload', payload);
        updateFinalPayload.order_level = {...updateFinalPayload.order_level, ...payload.order_level};
        removeFromAttentionItems("deliver_to", "order_level");
        setFinalDisputePayload({...updateFinalPayload});
    }

    const handleRejectDeliveryToCounter = () => {
        const payload = formatDisputePayload("orderLevel", "deliver_to", {
            line1: initialDeliveryToCounterData?.line1,
            line2: initialDeliveryToCounterData?.line2?.trim() || null,
            city: initialDeliveryToCounterData?.city,
            state_id: initialDeliveryToCounterData?.state_id,
            zip: initialDeliveryToCounterData?.zip,
            counter_id: initialDeliveryToCounterData?.counter_id,
        }, "reject");
        console.log('payload', payload);
        updateFinalPayload.order_level = {...updateFinalPayload.order_level, ...payload.order_level};
        removeFromAttentionItems("deliver_to", "order_level");
        setFinalDisputePayload({...updateFinalPayload});
    };

    const checkDeliveryAction = (deliverKey: string, actionToCheck: string) => {
        if (
          finalDisputePayload &&
          finalDisputePayload?.order_level &&
          (deliverKey === "deliver_to" || deliverKey === "deliver_by")
        ) {
          const deliveryObj = finalDisputePayload?.order_level?.[deliverKey];
          if (deliveryObj && deliveryObj?.action === actionToCheck) {
            return true;
          }
        }
        return false;
      }

      const handleUndoAction = (key: string) => {
        if (!finalDisputePayload?.order_level) return finalDisputePayload;
        // make a shallow copy of order_level
        const newOrderLevel = { ...finalDisputePayload.order_level };
        // delete the given key if it exists
        delete newOrderLevel[key];

        const updateInitialPoData = {...initialPoData};

        
        if(key === "deliver_by"){
            if(initialPoData?.order_level_dispute?.original_deliver_by_dispute){
                updateInitialPoData.order_level_dispute.deliver_by = [...initialPoData.order_level_dispute.original_deliver_by_dispute];
            }
        } 
        setSelectedQuote(updateInitialPoData);
        setInitialPoData(updateInitialPoData);
        setFinalDisputePayload( { ...finalDisputePayload, order_level: newOrderLevel });
        addToAttentionItems({ id: key, type: "order_level" });
      }
console.log('finalDisputePayload', finalDisputePayload);
    return (
        <div className={clsx(styles.headerInfoContainer)} ref={formInputGroupRef} data-hover-video-id='create-po-header'>
            {/* {(isCalendarOpen) && <div className={styles.calendarOpenOverlay}></div>} */}
            <div className={clsx(styles.createPoHeaderInfoGrid, !watch('isEdit') && styles.isEditMode)}>
                <div className={clsx(styles.leftGridHeader)}>
                    <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                        <div className={clsx(styles.col1, styles.poInputMain, styles.orderManagementPoName, watch('buyer_internal_po') && styles.hasValue)}>
                            <div className={styles.poNameLabel}>PO<span>:</span></div>
                            <div className={styles.pOInputValue}>
                                {watch('buyer_internal_po')} {(isSeller && selectedQuote?.seller_po_number) && <span className={styles.sellerPoNumber}> ({selectedQuote?.seller_po_number})</span>}
                            </div>
                        </div>
                    </div>
                    <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                        <div className={clsx(styles.deliverByContainer, styles.col1, styles.orderManagementPoName)}>
                            <div className={styles.poNameLabel}>Total Weight<span>:</span></div>
                            <div className={styles.pOInputValue}>{selectedQuote?.total_weight}</div>
                        </div>
                    </div>
                    <div className={clsx(styles.formInputGroup1, styles.formInputHeaderTop)}>
                        <div className={clsx(styles.deliverByContainer, styles.col1, styles.orderManagementPoName)}>
                            <div className={styles.poNameLabel}>Freight Term<span>:</span></div>
                            <div className={styles.pOInputValue}>Delivered</div>
                        </div>
                    </div>
                </div>
                <div className={styles.rightGridHeader}>
                        <span className={styles.poNameLabel}>
                            Buyer: <span>{initialPoData?.buyer_name ? initialPoData?.buyer_name : '-'}</span>
                        </span>
                        <span className={styles.poNameLabel}>
                            {initialPoData?.buyer_company_name ? initialPoData?.buyer_company_name : '-'}
                        </span>
                </div>

            </div>
            <div className={styles.deliveryDateContainer}>
                {initialPoData?.order_level_dispute?.deliver_by ?
                    (
                        <div className={styles.deliveryDateContainerCounterData}>

                            <div className={styles.deliveryDateContainerCounterDataHeading}>Delivery By:</div>
                            <div className={clsx(styles.disputeHeaderInfoGrid, !watch('isEdit') && styles.isEditMode)}>
                                <div className={styles.leftGridHeader}>
                                    <div className={styles.col1}>
                                        <div className={clsx(styles.pOInputValue,styles.scrollDisputeItem)} ref={deliverByContainerRef}>
                                            {initialPoData?.order_level_dispute?.deliver_by?.map((item: any, i: number) => {
                                                const isMe = userData?.data?.type === item?.created_by;
                                                const isBuyer = item?.created_by === userRole.buyerUser;
                                                const isRequesting = (isBuyer && item?.counter_status === disputeCounterStatus.pending);
                                                const counterLabel = item.counter_status === disputeCounterStatus.original ? "Originally" : isMe ? "Me " : isBuyer ? "Buyer" : "Supplier";
                                                return (
                                                    <div key={item.counter_id + i} className={styles.disputeItem}>
                                                        <label htmlFor={item.counter_id}>{counterLabel}</label>
                                                        <div className={clsx((i !== initialPoData?.order_level_dispute?.deliver_by?.length - 1) && styles.strikeThroughText)}>
                                                            {dayjs(item?.delivery_date).format('ddd, MMMM D, YYYY')}
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </div>

                                    </div>
                                </div>
                                <div className={styles.rightGridHeader}>
                                    
                                    <div className={clsx(styles.headingStatus, lastCounterFromSeller && styles.counterText)}>{!!(finalDisputePayload?.order_level?.deliver_by) ? 'In submission queue' : lastCounterFromSeller ? 'Countered' : ''}</div>
                                    <div className={styles.actionButtonsContainer}>
                                        <button className={clsx(styles.actionButtons,checkDeliveryAction("deliver_by", "accept") && styles.accepted)} onClick={handleAcceptCounter} disabled={(isSeller && (lastCounterFromSeller || finalDisputePayload?.order_level?.deliver_by ))}>Accept</button>
                                        <button onClick={handleCounterClick} ref={calendarAnchorRef} className={styles.actionButtons} disabled={!(lastCounterFromBuyer || inPayloadDeliveryByCounterPresent )}>Counter</button>
                                        <button className={clsx(styles.actionButtons,checkDeliveryAction("deliver_by", "reject") && styles.rejected)} onClick={handleRejectCounter} disabled={(isSeller && (lastCounterFromSeller || finalDisputePayload?.order_level?.deliver_by ))}>Reject</button>
                                        {
                                        !!(finalDisputePayload?.order_level?.deliver_by) && (
                                            <Tooltip title={undo} arrow 
                                               placement="bottom" disableInteractive TransitionComponent={Fade} 
                                               TransitionProps={{ timeout: 200 }} classes={{ tooltip: styles.undoTooltip }}
                                               >
                                               <button onClick={() => handleUndoAction("deliver_by")} className={styles.undoIcon}><UndoIcon /></button>
                                            </Tooltip>
                                        )
                                    }
                                    </div>
                                    <Popover
                                        open={isCalendarOpen}
                                        anchorEl={calendarAnchorRef.current}
                                        anchorOrigin={{
                                            vertical: 'bottom',
                                            horizontal: 'center',
                                        }}
                                        transformOrigin={{
                                            vertical: 'top',
                                            horizontal: 'center',
                                        }}
                                        classes={{
                                         paper: styles.disputeCalendarPopover
                                         }}
                                    >
                                        <div className={styles.calendarWrapper}>
                                            <Calendar
                                                setValue={(field: string, value: string) => {
                                                    if (field === 'delivery_date') {
                                                        console.log('value', value);
                                                    }
                                                }}
                                                isCalendarOpen={isCalendarOpen}
                                                setIsCalendarOpen={setIsCalendarOpen}
                                                disableDeliveryDate={false}
                                                handleOpenCalendar={handleOpenCalendar}
                                                saveUserActivity={() => { }}
                                                saveBomHeaderDetails={() => { }}
                                                onDateSelect={handleClickAway}
                                                allowedDates={[]}
                                                parentClassName={isCalendarOpen}

                                            />
                                        </div>
                                    </Popover>

                                </div>
                                <div>
                                </div>
                            </div>
                        </div>
                    ) :
                    <>
                        <div>Delivery By:</div>
                        <span className={styles.deliveryDateContainerData}>{initialPoData?.delivery_date ? dayjs(initialPoData?.delivery_date).format("ddd, MMMM DD, YYYY") : '-'}</span>
                    </>
                }

                {
                    currentFocusedItem?.id === 'deliver_by' && (
                        <span className={styles.pointRight}>
                            <PointRightIcon />
                        </span>
                    )
                }
            </div>
            <div className={styles.deliveryDateContainer}>
                {initialPoData?.order_level_dispute?.deliver_to ?
                    (
                        <div className={styles.deliveryDateContainerCounterData}>
                            <div className={styles.deliveryDateContainerCounterDataHeading}>Delivery To:</div>
                            <div className={clsx(styles.disputeHeaderInfoGrid, !watch('isEdit') && styles.isEditMode)}>
                                <div className={styles.leftGridHeader}>
                                    <div className={styles.col1}>
                                        <div className={styles.pOInputValue}>
                                            {initialPoData?.order_level_dispute?.deliver_to?.map((item: any) => {
                                                const isMe = userData?.data?.type === item?.created_by;
                                                const isBuyer = item?.created_by === userRole.buyerUser;
                                                const isRequesting = item?.counter_status === disputeCounterStatus.pending;
                                                const counterLabel = item.counter_status === disputeCounterStatus.original ? "Originally" : isRequesting ? "Requesting" : isMe ? "Me " : isBuyer ? "Buyer" : "Supplier";
                                                return (
                                                    <div key={item.counter_id} className={styles.disputeItem}>
                                                        <label htmlFor={item.counter_id}>{counterLabel}</label>
                                                        <div className={clsx(item.counter_status !== disputeCounterStatus.pending && styles.strikeThroughText)}>
                                                            {formatShippingAddress(item)}
                                                        </div>
                                                    </div>
                                                )
                                            })}
                                        </div>

                                    </div>
                                </div>
                                <div className={styles.rightGridHeader}>
                                    {
                                        !Boolean(selectedQuote?.order_level_dispute?.state_zip_dispute) && (
                                            <>
                                                
                                            <div className={styles.headingStatus}>{!!(finalDisputePayload?.order_level?.deliver_to) ? 'In submission queue' : ''}</div>
                                            <div className={styles.actionButtonsContainer}>
                                                <button className={clsx(styles.actionButtons,checkDeliveryAction("deliver_to", "accept") && styles.accepted)} onClick={handleAcceptDeliveryToCounter} disabled={isEditingPo}>Accept</button>
                                                <button className={clsx(styles.actionButtons,checkDeliveryAction("deliver_to", "reject") && styles.rejected)} onClick={handleRejectDeliveryToCounter} disabled={isEditingPo}>Reject</button>
                                                {
                                                    !!(finalDisputePayload?.order_level?.deliver_to) && (
                                                        <Tooltip title={undo} arrow placement="bottom" 
                                                        disableInteractive TransitionComponent={Fade} 
                                                        TransitionProps={{ timeout: 200 }} classes={{ tooltip: styles.undoTooltip }}>
                                                          <button onClick={() => handleUndoAction("deliver_to")} className={styles.undoIcon}><UndoIcon /></button>
                                                        </Tooltip>
                                                    )
                                                }
                                            </div>
                                            </>
                                        )
                                    }
                                </div>
                            </div>
                        </div>
                    ) :
                    <>
                        <div>Delivery To:</div>
                        <span className={styles.deliveryDateContainerData}>{formatShippingAddress(initialPoData?.shipping_details)}</span>
                    </>
                }
                {
                    currentFocusedItem?.id === 'deliver_to' && (
                        <span className={styles.pointRight}>
                            <PointRightIcon />
                        </span>
                    )
                }
            </div>
        </div>
    )
})

export default SellerHeaderInfo;