.container {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;


  .modal {
    border-radius: 12px;
    padding: 55px;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 9999999;
    border-radius: 20px;
    background: url(../../assets/New-images/New-Image-latest/new-update-popup-background.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  .closeButton {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    color: #888;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  
    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  .title {
    font-family: Syncopate;
    font-size: 28px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: -1.12px;
    text-align: left;
    color: #fff;
    text-transform: uppercase;
  }
  
  .versionInfo {
    font-family: Inter;
    font-size: 18px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: normal;
    text-align: left;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 16px;
    margin-bottom: 24px;
  }
  
  .updateContents {
    margin-bottom: 32px;
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.16px;
    text-align: left;
    color: #fff;
    border-radius: 16px;
    background-color: rgba(255, 255, 255, 0.04);
    padding: 16px;
    height: 60%;
  
    // Style for release notes content
    :global(.releaseNotesContent) {
      overflow-y: auto;
      height: 95%;
      &::-webkit-scrollbar {
        width: 5px;
        height: 6px;
      }
  
      ul {
        list-style: none;
        padding: 16px 0 0 16px;
        margin: 0;
      }
  
      li {
        list-style: none;
        padding: 6px 0;
        margin: 0;
        display: flex;
        align-items: center;
        
        // Add arrow icon using CSS
        &::before {
          content: "";
          background-image: url('../../assets/New-images/icon-right-arrow.svg');
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          width: 20px;
          height: 20px;
          margin-right: 8px;
          display: inline-block;
          filter: brightness(0) invert(1); // Makes the SVG white
        }
      }
    }
  }
  
  .featureList {
    list-style: none;
    padding: 0;
    margin: 0;
  
    li {
      display: flex;
      align-items: center;
      color: #ffffff;
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 12px;
      padding: 8px 0;
    }
  }
  
  .featureIcon {
    color: #4a9eff;
    font-size: 18px;
    margin-right: 12px;
    font-weight: bold;
    display: inline-block;
    width: 20px;
    text-align: center;
  }
  
  .actionButtons {
    display: flex;
    gap: 24px;
    justify-content: center;
  }

  .downloadText{
    font-family: Inter;
    font-size: 16px;
    font-weight: normal;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.16px;
    text-align: left;
    color: #fff;
    margin-top: 16px;
    margin-bottom: 24px;
    justify-content: center;
    display: flex;
    align-items: center;
    .downloadButton{
      margin-left: 8px;
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.16px;
      text-align: left;
      color: #fff;
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: underline;
    }
  }
  
  .keepCurrentButton,
  .updateAppButton {
    border: none;
    font-family: Syncopate;
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.72px;
    text-align: center;
    transition: all 0.2s ease;
    width: 50%;
    border-radius: 10px;
    padding: 8px 0;

    .updateAppButtonText{
      font-family: Inter;
      font-size: 12px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.16px;
    }
  }
  
  .keepCurrentButton {
    background-color: #222329;
    color: rgba(255, 255, 255, 0.4);
    &:hover {
      color: rgba(255, 255, 255);
    }
  }
  
  .updateAppButton {
    background-color: #ffffff;
    color: #0f0f14;
  }
  
}

.appDrag {
  -webkit-app-region: drag;
  width: 88%;
  height: 55px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  border-radius: 12px 0px 0px 0px;
}

.content {
  background: url(../../assets/New-images/AppBG.svg) no-repeat;
  background-size: cover;
  border: 1px solid #2a2a2a;
  border-radius: 20px;
  padding: 48px 32px;
  text-align: center;
  width: 100%;
  min-width: 400px;
  max-width: 800px;
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.header{
  width: 100%;
  display: flex;
  .dragPanel{ 
    -webkit-app-region: drag;
    flex: 1;
    position: absolute;
    top: 0;
    left: 0;
    right:100px;
    height: 55px;
  }
}

.windowControls {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.minimizeBtn,
.closeBtn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.1);
  color: #888888;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #cccccc;
  }

  svg {
    width: 12px;
    height: 12px;
  }
}

.closeBtn {
  &:hover {
    background: rgba(255, 0, 0, 0.2);
    color: #ff4444;
  }
}

.minimizeBtn {
  &:hover {
    background: rgba(255, 255, 0, 0.2);
    color: #ffff44;
  }
}

.icon {
  margin-bottom: 24px;
  color: #00ff00;

  svg {
    width: 120px;
    height: auto;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.loadingIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #333333;
  border-top: 2px solid #00ff00;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loadingText {
  font-size: 14px;
  color: #888888;
  font-weight: 500;
  font-family: Inter;
}

.appUpdateNotifiMain {
  display: flex;
  flex-direction: column;
  align-items: center;


  .title {
    font-size: 28px;
    font-weight: 600;
    color: #70ff00;
    margin: 0 0 20px 0;
    line-height: 1.2;
    font-family: Inter;
  }

  .message {
    font-size: 18px;
    color: #fff;
    line-height: 1.6;
    margin: 0 0 40px 0;
    font-family: Inter;
  }

  .updateLink {
    background: none;
    border: none;
    color: #00ff00;
    font-size: 18px;
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
        font-family: Inter;


    &:hover {
      color: #00cc00;
      background: transparent;
      text-decoration: none;
    }

    &:focus {
      outline: 2px solid #00ff00;
      outline-offset: 2px;
      border-radius: 4px;
    }
  }

}