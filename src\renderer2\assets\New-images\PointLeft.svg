<svg width="30" height="39" viewBox="0 0 30 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_104_1427)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M25.2374 31C22.8343 31 20.5727 29.9043 18.9963 28.0905C14.2778 22.6618 10.4901 19.5403 4 15.5C10.8854 11.096 14.5531 7.89728 19.0105 2.84061C20.5753 1.06554 22.8068 0.00370779 25.1731 0.00248807L30 0L30 31H25.2374ZM22.3101 24.9407L12.717 15.5L22.3114 6.0607C23.3399 5.0488 24.7082 4.48368 26.1316 4.48295L29.9992 4.48096V26.519H26.1337C24.709 26.519 23.3394 25.9537 22.3101 24.9407Z" fill="#FFB800"/>
</g>
<defs>
<filter id="filter0_d_104_1427" x="0" y="0" width="34" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_104_1427"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_104_1427" result="shape"/>
</filter>
</defs>
</svg>
