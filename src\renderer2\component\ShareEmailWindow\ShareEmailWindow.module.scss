.RightWindowcontainer {
    width: 100%;
    padding: 32px 16px 21px 16px;
    border-radius: 16px;
    background-origin: border-box;
    position: relative;
    // overflow: hidden;
    position: relative;
    margin-top: 16px;
    align-items: center;
    background-color: #191a20;
    &.inviteUser{
        padding: 16px 16px 21px 16px;
        .crossContainer {
            top: 0px;
        }
    }

    .title {
        font-family: Syncopate;
        font-size: 14px;
        font-weight: normal;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.5;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
        padding-bottom: 24px;
    }

    .shareTitle {
        font-family: Syncopate;
        font-size: 20px;
        font-weight: bold;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.25;
        letter-spacing: 5.8px;
        text-align: center;
        color: #fff;
        margin-top: 22px;
        margin-bottom: 12px;
    }

    .content {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 100%;

        .emailInputContainer {
            width: 100%;

            .emailTagsContainer {
                display: flex;
                width: 100%;
                height: 64px;
                background: url(../../assets/New-images/Share-Pricing/Email.svg) no-repeat transparent;
                background-position: bottom right;
                box-shadow: inset 4px 5px 2.2px 0 #000;
                background-size: cover;
                border-radius: 12px;
                border: none;
                min-height: 40px;
                align-items: center;
                position: relative;
                padding: 10px;
                flex-wrap: wrap;
                overflow: auto;
                gap: 4px;

                &:focus-within {
                    box-shadow: inset 4px 5px 2.2px 0 #000;
                    border: 0px solid transparent;
                    color: #fff;
                    background-image: linear-gradient(126deg, #1c40e7 -20%, #16b9ff 114%), linear-gradient(286deg, #fff 116%, #1a1b20 30%);
                    &::placeholder {
                        color: #fff;
                    }
                }

                &::-webkit-scrollbar {
                    width: 5px;
                    height: 6px;
                }

                .emailInput {
                    flex: 1;
                    min-width: 100px;
                    width: 100%;
                    background: transparent;
                    border: none;
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1;
                    letter-spacing: normal;
                    text-align: left;
                    color: #fff;
                    outline: none;
                    padding: 4px;
                    resize: none;

                    &::placeholder {
                        font-family: Inter;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 1;
                        letter-spacing: normal;
                        text-align: left;
                        color: rgba(255, 255, 255, 0.4);
                    }
                }

                .emailTag {
                    display: flex;
                    align-items: center;
                    padding: 0px 8px;
                    transition: background-color 0.2s ease;
                    border-radius: 12px;
                    background-color: rgba(255, 255, 255, 0.16);
                    font-family: Inter;
                    font-size: 12px;
                    font-weight: normal;
                    line-height: 1;
                    letter-spacing: normal;
                    text-align: left;
                    color: #fff;
                    height: 18px;
                    max-width: 264px;
                    overflow: hidden;

                    .emailText {
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    &:last-child {
                        margin-right: 4px;

                        &:after {
                            content: '';
                            position: absolute;
                            right: 10px;
                            width: 1px;
                            height: 16px;
                            background-color: rgba(255, 255, 255, 0.2);
                            animation: blink 1s infinite;
                            opacity: 0;
                        }
                    }

                    .removeTag {
                        margin-left: 6px;
                        padding-top: 1px;
                        cursor: pointer;
                        font-family: Syncopate;
                        font-size: 10px;
                        font-weight: bold;
                        line-height: 1;
                        letter-spacing: normal;
                        text-align: left;
                        color: rgba(255, 255, 255, 0.52);
                        flex-shrink: 0;
                    }
                }
            }
        }

        .messageContainer {
            margin: 18px 0px 16px 0px;
            position: relative;
            width: 100%;
            height: 210px;
            padding: 17px 8px 12px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            overflow-y: auto;

            &::-webkit-scrollbar {
                width: 5px;
                height: 6px;
            }
            .messageEditor {
                outline: none;
                word-break: break-word;
                font-family: Inter;
                font-size: 12px;
                font-weight: normal;
                line-height: 1.5;
                letter-spacing: normal;
                text-align: left;
                color: #fff;
                min-height: 90px;
                margin-bottom: 16px;

                :global(.placeholder) {
                    color: #fff;
                }

                b,
                strong {
                    font-weight: bold;
                }

                i,
                em {
                    font-style: italic;
                }

                u {
                    text-decoration: underline;
                }
            }
        }

        .inputfiled {
            width: 100%;
            height: 40px;
            padding: 8px 12px;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Inter', sans-serif;
            font-size: 14px;

            &:focus {
                outline: none;
                border-color: rgba(255, 255, 255, 0.3);
            }
        }

        .errorInput {
            border-color: rgba(255, 0, 0, 0.5);

            &:focus {
                border-color: rgba(255, 0, 0, 0.7);
            }
        }

        .errorText {
            color: #ff4859;
            font-size: 12px;
            margin-left: 6px;
            margin-top: 1px;
            font-family: 'Inter', sans-serif;
            position: absolute;

            &.duplicateError {
                color: #ffa500;
                font-weight: 500;
                display: flex;
                align-items: center;

                &:before {
                    content: '⚠️';
                    margin-right: 5px;
                    font-size: 14px;
                }
            }

            &.sameEmailError {
                color: #ff4859;
                font-weight: 500;
                display: flex;
                align-items: center;

                &:before {
                    content: '⛔';
                    margin-right: 5px;
                    font-size: 14px;
                }
            }
        }

        .sendInvitesButton {
            margin-top: 8px;
            font-family: Inter;
            font-size: 12px;
            font-weight: normal;
            line-height: 1.3;
            letter-spacing: normal;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
        }

        .buttonContainer {
            display: flex;
            justify-content: flex-end;
            width: 100%;

            .sendButton {
              width: 100px;
                height: 31px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: center;
                padding: 4px 0 0;
                border-radius: 4px;
                box-shadow: 0 -5.3px 5.3px 0 rgba(0, 0, 0, 0.8);
                background-image: linear-gradient(144deg, #1c40e7 -60%, #16b9ff 139%);
                transition: all 0.1s;
                font-family: Syncopate;
                font-size: 14px;
                font-weight: bold;
                font-style: normal;
                line-height: 1;
                letter-spacing: -0.56px;
                text-align: center;
                color: #fff;
                &:disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                }
            }

            .sendButtonDisabled {
                width: 100px;
                height: 31px;
                display: flex;
                flex-direction: row;
                justify-content: center;
                align-items: flex-start;
                padding: 4px 0 0;
                border-radius: 4px;
                background-color: rgba(255, 255, 255, 0.04);
                font-family: Syncopate;
                font-size: 14px;
                font-weight: bold;
                line-height: 1;
                letter-spacing: -0.56px;
                text-align: center;
                color: rgba(255, 255, 255, 0.4);
                text-transform: uppercase;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: not-allowed;
            }
        }
    }
}

@keyframes buttonPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 83, 44, 0.4);
    }

    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 5px rgba(255, 83, 44, 0.0);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 83, 44, 0.0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
        transform: translate(-50%, 10px);
    }

    100% {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

@keyframes blink {

    0%,
    100% {
        opacity: 0;
    }

    50% {
        opacity: 1;
    }
}

.emailTag {
    &.focused {
        background-color: rgba(255, 255, 255, 0.25);
    }
}

// Success View Styles
.successHeader {
    text-align: center;
    padding-top: 30px;

    h2 {
        font-family: Inter;
        font-size: 16px;
        font-weight: 200;
        font-style: normal;
        line-height: 1.5;
        letter-spacing: 7.36px;
        text-align: center;
        color: #fff;
    }

    h1 {
        font-family: Syncopate;
        font-size: 20px;
        font-weight: bold;
        line-height: 1.5;
        letter-spacing: normal;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
        margin: 0;
    }
}

.successContent {
    padding: 0px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.successMessage {
    font-family: Inter;
    font-size: 16px;
    font-weight: 300;
    font-style: normal;
    line-height: 1.75;
    letter-spacing: -0.16px;
    text-align: center;
    color: #fff;
    margin-top: 40px;
    margin-bottom: 55px;
    padding: 0px 16px;

    i {
        font-style: italic;
    }
}

.shareAgainButton {
    width: 100%;
    height: 50px;
    border-radius: 10px;
    font-family: Syncopate;
    font-size: 16px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.3;
    letter-spacing: -0.64px;
    text-align: center;
    color: rgba(255, 255, 255, 0.4);
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    background-color: #222329;
    &:hover {
        background-image: linear-gradient(124deg, #1c40e7 -16%, #16b9ff 112%);
        color: #fff;
    }
}

.formattingToolbarContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .formattingToolbar {
        display: flex;
        align-items: center;
        column-gap: 2px;

        .emojiContainer {
            padding-top: 5px;

            .emojiPicker {
                position: absolute;
                bottom: 55px;
                left: 0;
                width: 100%;
                max-height: 70px;
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
                overflow-y: auto;
                z-index: 10;
                background-color: rgba(255, 255, 255, 0.04);
                padding-top: 3px;
                border-radius: 10.3px;

                &::-webkit-scrollbar {
                    width: 5px;
                    height: 6px;
                }


                &:before {
                    content: '';
                    position: absolute;
                    bottom: -8px;
                    left: 10px;
                    width: 0;
                    height: 0;
                    border-left: 8px solid transparent;
                    border-right: 8px solid transparent;
                    border-top: 8px solid rgba(0, 0, 0, 0.8);
                }

                .emojiOption {
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0;
                    margin: 0;
                    background: transparent;
                    border: none;
                    border-radius: 4px;
                    transition: background-color 0.2s;

                    &:hover {
                        background-color: rgba(255, 255, 255, 0.1);
                    }
                }
            }
        }

        .formattingButton {
            background: transparent;
            border: none;
            color: #fff;
            font-size: 18px;

            .Icon {
                width: 27.7px;
                height: 27.7px;
                display: block;
            }

            .IconHover {
                width: 27.7px;
                height: 27.7px;
                display: none;
            }

            &:hover {
                .Icon {
                    display: none;
                }

                .IconHover {
                    display: block;
                }
            }
        }

        .buttonClicked {
            .Icon {
                display: none;
            }

            .IconHover {
                display: block;
            }
        }

        .emojiButton {
            .Icon {
                display: none;
            }

            .IconHover {
                display: block;
            }
        }
    }
}

.crossContainer {
    display: flex;
    gap: 8px;
    position: absolute;
    top: 10px;
    right: 10px;

    .cross {
        cursor: pointer;
        &:hover{
            path{
                fill-opacity: 1;
            }
        }
    }

}

.ProductContainer {
    margin-top: auto;

    .selectedSearchProductTop {
        position: relative;
        margin-bottom: 10px;

        &:last-child {
            margin-bottom: 0px;
        }
    }

    .productDescriptionMain {
        transition: all 0.1s;
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;

        .firstLine {
            font-family: Inter;
            font-size: 12px;
            font-weight: 300;
            font-stretch: normal;
            font-style: normal;
            line-height: 1.4;
            letter-spacing: 0.84px;
            text-align: left;
            color: rgba(255, 255, 255, 0.4);
            transition: all 0.1s;
            text-transform: uppercase;
            font-weight: 600;
        }

        .searchProductDescContainer {
            display: flex;
            align-items: stretch;
            justify-content: space-between;

            .searchProductDesc {
                font-family: Inter;
                font-size: 12px;
                font-weight: 300;
                font-stretch: normal;
                font-style: normal;
                line-height: 1.4;
                letter-spacing: 0.84px;
                text-align: left;
                color: rgba(255, 255, 255, 0.4);
                transition: all 0.1s;
                text-transform: uppercase;
            }
        }

    }

    .priceRating {
        position: relative;
        font-family: Inter;
        font-size: 12px;
        font-weight: 300;
        font-stretch: normal;
        font-style: normal;
        line-height: 1.4;
        letter-spacing: 0.48px;
        text-align: right;
        color: rgba(255, 255, 255, 0.4);

        .priceMain {
            display: flex;
            justify-content: flex-end;
            width: 100%;
            height: 100%;
            position: relative;
        }

        .priceSelectedWrapper {
            display: flex;
            flex: 0 120px;
            margin-left: auto;
            justify-content: flex-end;
        }

        .priceSelected {
            font-size: 24px;
            font-weight: 300;
            font-stretch: normal;
            line-height: 1;
            text-align: right;
            color: #fff;
            display: flex;
            flex-direction: column;
            position: relative;

            .displayRow {
                display: flex;
                align-items: baseline;
            }

            .doller {
                margin: 0 2px 8px 0;
                font-family: Inter;
                font-size: 16px;
                font-weight: normal;
                line-height: 1;
                letter-spacing: normal;
                text-align: left;
                color: rgba(255, 255, 255, 0.4);
                position: absolute;
                top: 2px;
                left: -15px;
            }

            .priceUnit {
                font-family: Inter;
                font-size: 10px;
                font-weight: 300;
                line-height: 1.4;
                letter-spacing: 0.4px;
                text-align: right;
                color: rgba(255, 255, 255, 0.92);
                margin-top: 4px;

                .unitLbl {
                    color: rgba(255, 255, 255, 0.56);
                }
            }

        }

    }
}
.quoteContainer {
    display: flex;
    justify-content: space-between;
    font-family: Inter;
    font-size: 12px;
    font-weight: 300;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.4;
    letter-spacing: 0.84px;
    text-align: left;
    color: rgba(255, 255, 255, 0.4);
    .quoteTitle {
        font-weight: 600;
    }
    .quotePrice {
        font-weight: 600;
    }
}
.quotePriceContainer {
    text-align: right;
}