    .updatePopupMain {
        width: 400px;
        height: 100vh;
        display: flex;
        flex-direction: column;
        padding: 0;
        object-fit: contain;
        border-radius: 16px;
        padding: 20px;
        background-image: linear-gradient(91deg, #0f0f14 29%, #393e47 238%);
        .closeIcon {
            position: absolute;
            top: 10px;
            right: 12px;
            cursor: pointer;

            svg {
                height: 25px;
                width: 25px;

                path {
                    fill: #fff;
                    opacity: 0.5;
                }
            }
        }

        .updateAvailableTitle {
            font-family: Inter;
            font-size: 20px;
            font-weight: 600;
            line-height: 1.6;
            text-align: left;
            color: #fff;
            margin-bottom: 2px;
        }

        .updateAvailableText {
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            color: #fff;
        }

        .updateInfoBox {
            margin-top: 20px;
            margin-bottom: 30px;
            width: 100%;
            height: 156px;
            display: flex;
            flex-direction: column;
            padding: 12px 8px 0 16px;
            border-radius: 4px;
            border: solid 1px #333;
            overflow: auto;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1.4;
            text-align: left;
            color: #fff;
            &::-webkit-scrollbar {
                width: 5px;
                height: 6px;
              }
            ul{
                padding-left: 30px;
            }
        }

        .btnSection {
            display: flex;
            justify-content: flex-end;
            button {
                background-color: transparent;
                border: 0px;
                padding: 0;
            }

            .closeWidgetBtn {
                opacity: 0.7;
                font-family: Inter;
                font-size: 16px;
                font-weight: normal;
                line-height: 1.6;
                text-align: right;
                color: #fff;
            }

            .updateWidgetBtn {
                font-family: Inter;
                font-size: 16px;
                line-height: 1.6;
                text-align: right;
                color: #70ff00;
                margin-left: 16px;
            }
        }

    }
