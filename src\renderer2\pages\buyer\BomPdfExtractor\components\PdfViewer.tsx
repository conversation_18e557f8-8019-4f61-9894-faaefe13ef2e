import React, { useState, useRef, useCallback, useEffect, RefObject, forwardRef, useMemo } from 'react'; 
import * as pdfjs from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.min?url'

// Remove the direct worker import and use dynamic import instead
// import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import PdfTextExtractorOCR from '../PdfTextExtractorOCR';
import PdfTextExtractor from '../PdfTextExtractor';
import Toolbar from './Toolbar';
import '../styles/App.css';
import { BOX_TYPES } from '../config/boxTypes';
import textractService from '../services/textractService';
import { mapBoxesToTextractData, processTextractData } from '../utils/textractMapper';
import { FEATURES, API_CONFIG } from '../config';
import { 
  TextractResult, 
  PdfTextExtractorProps, 
  PdfTextExtractorOCRProps,
  PdfExtractorRefMethods 
} from '../types/PdfExtractorTypes';
import { useBomPdfExtractorStore } from '../BomPdfExtractorStore';
import { useDropzone } from 'react-dropzone';
import BomUploadImage from "../../../../assets/New-images/BomUploadImg.png";
import styles from '../styles/BomExtractor.module.scss';
import LeftToolBar from './LeftToolBar';
import PdfNavigator from './PdfNavigator';
import clsx from 'clsx';
import { commomKeys, orderType, useCreatePoStore } from '@bryzos/giss-ui-library';
import { set } from 'react-hook-form';
import useDialogStore from 'src/renderer2/component/DialogPopup/DialogStore';
import { useLocation, useNavigate } from 'react-router-dom';
import { Split } from '@geoffcox/react-splitter';
import BOMExtractedDataGrid from './BOMExtractedDataGrid';
import CustomSplitterHandle from './CustomSplitterHandle';
import { routes } from 'src/renderer2/common';
import ResizablePanels, { ResizablePanelsRef } from 'src/renderer2/component/BomProcessingWindow/ResizablePanels';

// Setup worker using dynamic import
pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker
// pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`

interface PdfViewerProps {
  onStateChange?: (state: PdfViewerState) => void;
}

interface PdfViewerState {
  pdfFile?: File | null;
  isImageBasedPdf?: boolean;
  numPages?: number;
  pageNumber?: number;
  boxes?: any[];
  extractedData?: any;
  textractData?: TextractResult | null;
  hasBoxes?: boolean;
  hasExtractedData?: boolean;
  currentBoxType?: string;
  gridOpacity?: number;
  showMagnifyingGlass?: boolean;
  autoSelectColumns?: boolean;
  overlapPercent?: number;
  usingTextract?: boolean;
  extractionStats?: ExtractionStats;
  isimagaeBasedPdf?:boolean;
}

interface ExtractionStats {
  [key: string]: any;
}

// Forward ref types for the components
const PdfTextExtractorOCRWithRef = forwardRef<PdfExtractorRefMethods, PdfTextExtractorOCRProps>((props, ref) => {
  return <PdfTextExtractorOCR {...props} ref={ref} />;
});

// const PdfTextExtractorWithRef = forwardRef<PdfExtractorRefMethods, PdfTextExtractorProps>((props, ref) => {
//   return <PdfTextExtractor {...props} ref={ref} />;
// });

const PdfViewer: React.FC<PdfViewerProps> = ({ onStateChange }) => {
  // Component state
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [currentBoxType, setCurrentBoxType] = useState<string>('description');
  const [hasBoxes, setHasBoxes] = useState<boolean>(false);
  const [hasExtractedData, setHasExtractedData] = useState<boolean>(false);
  const [snapToGrid] = useState<boolean>(true); // Always true now
  const [gridOpacity, setGridOpacity] = useState<number>(0.7); // Default grid opacity
  const [showMagnifyingGlass, setShowMagnifyingGlass] = useState<boolean>(true);
  const [overlapPercent, setOverlapPercent] = useState<number>(40);
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [extractionStats, setExtractionStats] = useState<ExtractionStats | null>(null);

  // AWS Textract integration
  const [isTextractProcessing, setIsTextractProcessing] = useState<boolean>(false);
  const [textractData, setTextractData] = useState<TextractResult | null>(null);
  const [processedTextractData, setProcessedTextractData] = useState<any>(null);
  //const [textractRBushInitialized, setTextractRBushInitialized] = useState<boolean>(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const ocrComponentRef = useRef<PdfExtractorRefMethods>(null);
  const [isDragActive, setIsDragActive] = useState<boolean>(false);
  const { search } = useLocation();
  const params = new URLSearchParams(search);
  const isBack = params.get('isBack');
  const resizablePanelsRef = useRef<ResizablePanelsRef>(null);
  const datagridRef = useRef<any>(null)

  const {setPdfFile, setBomData, pdfFile, setPdfUrl, setPdfFileName,pdfFileName, textractRBushInitialized, setTextractRBushInitialized, doResetAll, 
    setS3Url,setBomUploadID, isImageBasedPdf, setIsImageBasedPdf, resetBomPdfExtractorStore, doResetBomPdfExtractorStore, pdfUrl, setShowBackToBomUploadButton, 
    bomUploadID, allBoxes, setAllBoxes, setCustomBoxTypes, setGridRowData } = useBomPdfExtractorStore();
  const { showCommonDialog, resetDialogStore } = useDialogStore.getState();
  const {uploadBomInitialData, setUploadBomInitialData, setIsCreatePOModule} = useCreatePoStore();
  const location = useLocation();
  const newFile = location.state?.file;
  // Add ref for the browse button
  const browseButtonRef = useRef<HTMLButtonElement>(null);
    const navigate = useNavigate();

  useEffect(()=>{  
    if(isBack && bomUploadID){
      loadPDF();
    }
  },[isBack, bomUploadID]);

  const loadPDF = async()=>{
    try{
      let pdfUrl = useBomPdfExtractorStore.getState().pdfUrl;
      let pdfFileName = useBomPdfExtractorStore.getState().pdfFileName;

      if(!pdfUrl || !pdfFileName){
        const bomData = await textractService.getBomData(bomUploadID);
        setPdfUrl(bomData.data.s3_url);
        setPdfFileName(bomData.data.actual_file_name);
        pdfUrl = bomData.data.s3_url;
        pdfFileName = bomData.data.actual_file_name;
      }
      //first get the geometry data
      const geometryData = await textractService.getGeometryData(bomUploadID);
      let sectionData;
      if(!geometryData?.selection_data) {
        sectionData = {geometryData:[], customBoxTypes:[], gridData:[]};
      }else{
        sectionData = JSON.parse(geometryData.selection_data);
      };
      setAllBoxes(sectionData.geometryData? sectionData.geometryData : []);
      setCustomBoxTypes(sectionData.customBoxTypes? sectionData.customBoxTypes : []);
      setGridRowData(sectionData.gridData? sectionData.gridData : []);

      const pdf = await pdfjs.getDocument(pdfUrl).promise;
      const data = await pdf.getData();
      const file = new File([data], pdfFileName, { type: 'application/pdf' });

      

      // Update state with PDF information
      setNumPages(pdf.numPages);

      const page = await pdf.getPage(1);
      const content = await page.getTextContent();

      // If there are very few text items, consider it an image-based PDF
      const isImageBased = !content.items || content.items.length < 5;
      // Set PDF file and type first so it's immediately visible to the user
      if (isImageBased) {
        setIsImageBasedPdf(true);
        setPdfFile(file);
        const extractedData = await textractService.getExtractedText(bomUploadID);
        const textractData = {
          Blocks:extractedData.data.json_data,
          DocumentMetadata:{Pages:pdf.numPages}
        }
        
        setTextractData(textractData)
        //setPdfFileName(file.name);
      } else {
        setIsImageBasedPdf(false);
        setPdfFile(file);
        //setPdfFileName(file.name);
      }
      if (onStateChange) {
        onStateChange({
          pdfFile: file,
          isImageBasedPdf,
          numPages: pdf.numPages,
          pageNumber: 1,
          usingTextract: FEATURES.useTextract,
        });
      }
    }catch(error){
      console.log(error);
    }
  
  }


  useEffect(()=>{
    setBomData(null);
    
    return ()=>{
      //do not reset bom n unload if moving to review page. otherwise reset. 
      if(doResetBomPdfExtractorStore.value){
        //resetBomPdfExtractorStore();
        //unloadAll();
      }
      //reset it to true 
      doResetBomPdfExtractorStore.value = true;
    }
  },[]);

  useEffect(()=>{
    if(newFile){
      console.log("calling reset ...")
      resetBomPdfExtractorStore();
      handleFileChange(newFile);
    }
  },[newFile]);

  // useEffect to focus the browse button when component mounts
  useEffect(() => {
    if (!pdfFile && browseButtonRef.current) {
      // Small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        browseButtonRef.current?.focus();
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [pdfFile]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    handleFileChange(acceptedFiles[0]);
  }, []);

  const {
    getRootProps,
    getInputProps,
    open,
    isDragAccept,
    isDragReject
} = useDropzone({
    onDrop,
    noClick: true,
    accept: {
        'application/vnd.ms-excel': ['.xls'],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
        'text/csv': ['.csv'],
        'application/pdf': ['.pdf']
    },
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    onDropAccepted: () => setIsDragActive(false),
    onDropRejected: () => setIsDragActive(false)
});

const handleKeyDown = (e: KeyboardEvent<HTMLButtonElement>) => {
  if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      open();
  }
};

const handleButtonClick = () => {
  open();
};







  // Create a wrapper for onError to handle the type mismatch
  const handleError = useCallback((error: Error) => {
    setErrorMessage(error.message);
  }, []);

  // Handle file upload and check if it's an image-based PDF
  const handleFileChange = async (file: File) => {
    //const file = e.target.files?.[0] || null;
    if (!file) return;

    if (!file.type.includes('pdf')) {
      const errorMsg = 'Please select a PDF file.';
      setErrorMessage(errorMsg);
      return;
    }

    try {
      // Reset state
      setErrorMessage('');
      setPdfFile(null);
      //setIsImageBasedPdf(false);
      setTextractData(null);
      setProcessedTextractData(null);
      setTextractRBushInitialized(false);

      // Check if the PDF is image-based or has a text layer
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;

      // Update state with PDF information
      setNumPages(pdf.numPages);

      const page = await pdf.getPage(1);
      const content = await page.getTextContent();

      // If there are very few text items, consider it an image-based PDF
      const isImageBased = !content.items || content.items.length < 5;
      // Set PDF file and type first so it's immediately visible to the user
      if (isImageBased) {
        setIsImageBasedPdf(true);
        setPdfFile(file);
        setPdfFileName(file.name);
        setErrorMessage('Image-based PDF detected. Using OCR extraction.');
      } else {
        setIsImageBasedPdf(false);
        setPdfFile(file);
        setPdfFileName(file.name);
        setErrorMessage('Text-based PDF detected. Using direct text extraction.');
      }

      // Start AWS Textract processing in the background
      if (FEATURES.useTextract) {
        // Start processing in the background
        setIsTextractProcessing(true);
        setErrorMessage(prev => `${prev} Uploading to AWS Textract in the background...`);

        // Use a non-blocking approach with Promise
        textractService.uploadAndProcessPdf(file, uploadBomInitialData, isImageBased)
          .then((result: any) => {
            const textractResult = result.textractData as TextractResult;
            console.log(result)
            setS3Url(result.s3Url);
            console.log("setting bom upload id");
            setBomUploadID(result.bomUploadID);
            setUploadBomInitialData({...uploadBomInitialData, bom_id:result.bomUploadID})
            // Process the Textract data for easier use
            const processed = processTextractData(textractResult);
            if(textractResult)
              setTextractData(textractResult);
              setProcessedTextractData(processed);
          })
          .catch(textractError => {
              const path = uploadBomInitialData?.order_type === orderType.QUOTE ? routes.quotePage : routes.createPoPage;
              setShowBackToBomUploadButton(false);
              setIsCreatePOModule(false);
              navigate(path, { state: { from: 'bomPdfExtractor' } });
              //showCommonDialog(null,textractError?.message || commomKeys.errorContent, null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
              showCommonDialog(null,'Error uploading the pdf. Please try again.', null, resetDialogStore, [{name: commomKeys.errorBtnTitle, action: resetDialogStore}])
              //setErrorMessage(`PDF loaded, but AWS Textract processing failed: ${textractError.message}. Falling back to standard extraction.`);
          })
          .finally(() => {
            setIsTextractProcessing(false);
          });
      }

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({
          pdfFile: file,
          isImageBasedPdf,
          numPages: pdf.numPages,
          pageNumber: 1,
          usingTextract: FEATURES.useTextract,
        });
      }
    } catch (error: any) {
      const errorMsg = 'Error loading PDF. Please try again.';
      setErrorMessage(errorMsg);
    }
  };


  const unloadAll = () => {
    setPdfFile(null);

    setIsImageBasedPdf(false);
    setErrorMessage('');
    setHasBoxes(false);
    setHasExtractedData(false);
    setTextractData(null);
    setProcessedTextractData(null);
    setIsTextractProcessing(false);
    setTextractRBushInitialized(false);

    // Update app state for debugging
    if (onStateChange) {
      onStateChange({
        pdfFile: null,
        isImageBasedPdf: false,
        numPages: 0,
        pageNumber: 1,
        boxes: [],
        extractedData: null,
        textractData: null,
      });
    }
  };

  // These functions will be called by the OCR component to update the parent state
  const handleBoxesChange = (hasAnyBoxes: boolean) => {
    // Only update state if there's an actual change
    if (hasAnyBoxes !== hasBoxes) {
      setHasBoxes(hasAnyBoxes);

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({ hasBoxes: hasAnyBoxes });
      }
    }
  };

  const handleExtractedDataChange = (hasAnyData: boolean) => {
    setHasExtractedData(hasAnyData);
    if(resizablePanelsRef.current){
      resizablePanelsRef.current.setSizes([0.2, 0.8], true);
    }
  };

  // Add a function to receive extraction statistics
  const handleExtractionStats = (stats: ExtractionStats) => {
    // Only update if stats have changed
    if (JSON.stringify(stats) !== JSON.stringify(extractionStats)) {
      setExtractionStats(stats);

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({ extractionStats: stats });
      }
    }
  };

  // These functions will be passed to the OCR component
  const handleUndoBox = useCallback(() => {
    if (ocrComponentRef.current && ocrComponentRef.current.handleUndoBox) {
      ocrComponentRef.current.handleUndoBox();
    }
  }, []);

  const handleExtractText = useCallback(() => {
    // if (ocrComponentRef.current && ocrComponentRef.current.extractImages) {
    //   ocrComponentRef.current.extractImages();
    // }
  }, []);

  const handleExportCSV = useCallback(() => {
    if (ocrComponentRef.current && ocrComponentRef.current.exportCSV) {
      ocrComponentRef.current.exportCSV();
    }
  }, []);

  // Placeholder for Process Doc functionality
  const handleProcessDoc = useCallback(() => {
    // No functionality implemented yet
  }, []);

  const handleBoxTypeChange = useCallback(
    (type: string) => {
      setCurrentBoxType(type);
      if (
        ocrComponentRef.current &&
        ocrComponentRef.current.setCurrentBoxType
      ) {
        ocrComponentRef.current.setCurrentBoxType(type);
      }

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({ currentBoxType: type });
      }
    },
    [onStateChange]
  );

  const handleShowMagnifyingGlassChange = useCallback(
    (isChecked: boolean) => {
      setShowMagnifyingGlass(isChecked);

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({ showMagnifyingGlass: isChecked });
      }
    },
    [onStateChange]
  );

  const handleOverlapPercentChange = useCallback(
    (value: number) => {
      setOverlapPercent(value);

      // Update app state for debugging
      if (onStateChange) {
        onStateChange({ overlapPercent: value });
      }
    },
    [onStateChange]
  );

  // Add effect to update debug state when page changes
  // Using a ref to track previous values to prevent unnecessary updates
  const prevValuesRef = useRef<{ pageNumber: number | null; numPages: number | null }>({
    pageNumber: null,
    numPages: null
  });

  // Initialize TextractRBush when Textract data is available
  useEffect(() => {
    if (textractData && ocrComponentRef.current && ocrComponentRef.current.initializeTextractRBush) {
      const textractRBushInstance = ocrComponentRef.current.initializeTextractRBush(textractData);
    } else {
    }
  }, [textractData]);

  useEffect(() => {
    if (pdfFile && onStateChange) {
      // Only update if values have changed
      const prevValues = prevValuesRef.current;
      if (
        pageNumber !== prevValues.pageNumber ||
        numPages !== prevValues.numPages
      ) {
        onStateChange({
          pageNumber,
          numPages,
        });

        // Update previous values
        prevValuesRef.current = { pageNumber, numPages };
      }
    }
  }, [pageNumber, numPages, pdfFile, onStateChange]);

  const handleExtractionComplete = useCallback((result: TextractResult) => {
    setTextractData(result);
    setHasExtractedData(true);
  }, []);

  const splitContainerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  useEffect(() => {
    const updateWidth = () => {
      if (splitContainerRef.current) {
        setContainerWidth(splitContainerRef.current.clientWidth);
      }
    };

    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);
  const calculateInitialLeftWidth = useMemo(() => {
    if (!containerWidth) return "750px"; // fallback
    
    const minWidth = 750;
    const fiftyPercent = containerWidth * 0.5;
    
    // Use minimum width or 50% if container is large enough
    return containerWidth > minWidth*2 ? Math.max(minWidth, fiftyPercent)+"px" : minWidth+"px";
  }, [containerWidth]);
  

  return (
    <div className={styles.appContainer} style={{display:'flex', flexDirection:'row'}}>
      {/* <LeftToolBar /> */}
      <div className={clsx(styles.pdfViewerContainer,'card')} >
        <div className={styles.pdfInnerContainer}>
    
         
        <div className={styles.pdfContainerSplitter} ref={splitContainerRef}>
          <ResizablePanels 
            renderHandle={CustomSplitterHandle}
            handleStyle={{
              display:'flex',
              alignItems:'center',
              justifyContent:'center',
              width: 8
            }}
            ref={resizablePanelsRef}
            defaultSizes={[0.7, 0.3]}
            // onSplitChanged={(newSize) => {
            //   // Call the OCR component's resize handler
            //   if (ocrComponentRef.current && ocrComponentRef.current.handleSplitResize) {
            //     ocrComponentRef.current.handleSplitResize();
            //   }
            // }}
          >
          <div>
       {pdfFile && (<Toolbar/>)}
        <PdfTextExtractorOCRWithRef
            ref={ocrComponentRef}
            onExtractionComplete={handleExtractionComplete}
            onError={handleError}
            onBoxesChange={handleBoxesChange}
            onExtractedDataChange={handleExtractedDataChange}
            getRowData={
              () => datagridRef.current?.getRowData()
            }
          />
          </div>
           
          <div style={{width:'100%', height:'100%'}}><BOMExtractedDataGrid ref={datagridRef}/></div>
        </ResizablePanels>
        </div>
        
        </div>
      </div>
      {/* <div className={styles.pdfNavigator}>{pdfFile&&<PdfNavigator />}</div> */}
    </div>
  );
};

export default PdfViewer;
