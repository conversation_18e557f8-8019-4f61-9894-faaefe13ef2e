.videoPopupContainer {
  position: relative;
  background: #1a1a1a;
  color: white;
  padding: 0;
  border-radius: 12px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  &:fullscreen {
    border-radius: 0;
    max-width: 100vw !important;
    max-height: 100vh !important;
  }
}

.buttonContainer {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 1000;
  display: flex;
  gap: 8px;
}

.closeButton,
.fullscreenButton {
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
  pointer-events: auto;
  position: relative;
  z-index: 1001;

  &:hover {
    background: rgba(0, 0, 0, 0.8);
  }

  &:focus {
    outline: 2px solid white;
    outline-offset: 2px;
  }

  svg {
    width: 16px;
    height: 16px;
    fill: white;
    pointer-events: none;
  }
}

.videoSection {
  width: 100%;
  background: black;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  
  // Ensure video player fills the container properly
  :global(.custom-video-player) {
    border-radius: 0;
  }
}

// Fullscreen styles for different browsers
:global(.MuiDialog-paper):fullscreen {
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  border-radius: 0 !important;
}

:global(.MuiDialog-paper):-webkit-full-screen {
  max-width: 100vw !important;
  max-height: 100vh !important;
  margin: 0 !important;
  border-radius: 0 !important;
}

// Video fullscreen styles
:global(video):fullscreen {
  width: 100vw !important;
  height: 100vh !important;
  object-fit: contain;
}

:global(video):-webkit-full-screen {
  width: 100vw !important;
  height: 100vh !important;
  object-fit: contain;
}

// Hide dialog backdrop when video is fullscreen
:global(.MuiBackdrop-root):has(video:fullscreen) {
  display: none !important;
}

.infoSection {
  padding: 20px 24px 24px;
  background: #1a1a1a;
}

.title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
  line-height: 1.3;
}

.description {
  margin: 0;
  font-size: 14px;
  color: #cccccc;
  line-height: 1.5;
}
