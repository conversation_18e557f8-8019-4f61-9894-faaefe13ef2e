import React from 'react';
import styles from '../SellerHeaderInfo/SellerHeaderInfo.module.scss'
import clsx from 'clsx';

interface globalDisputeProps {
  /** The React component to render dynamically */
  component?: React.ComponentType<any> | null;
  /** Props to pass to the component */
  componentProps?: Record<string, any>;
}

const DynamicHeader: React.FC<globalDisputeProps> = ({ 
  component: Component, 
  componentProps = {} 
}) => {
  if (!Component) {
    return null;
  }

  // Use React.createElement to avoid potential hook issues
  return (
    <div className={clsx(styles.headerInfoContainer, styles.dynamicHeaderContainer)}>
      {React.createElement(Component, componentProps)}
    </div>
  );
};

export default DynamicHeader;
