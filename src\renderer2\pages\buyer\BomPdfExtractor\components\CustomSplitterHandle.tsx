import React from 'react';
import { ReactComponent as SplitIcon } from '../../../../assets/New-images/New-Image-latest/BOM-Extractor/split-icon.svg';
import styles from '../styles/BomExtractor.module.scss';
import clsx from 'clsx';
import { ResizeHandleProps } from 'src/renderer2/component/BomProcessingWindow/ResizablePanels';

const CustomSplitterHandle = (props: ResizeHandleProps) => {
  return (
    <div className={clsx(styles.customSplitterHandle, styles.dragging)} {...props}>
      <SplitIcon 
        className={clsx(
          styles.splitIcon,
        )}
      />
    </div>
  );
};

export default CustomSplitterHandle; 