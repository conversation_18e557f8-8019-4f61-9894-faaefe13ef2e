// @ts-nocheck

import { useGlobalStore } from "@bryzos/giss-ui-library";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { raygunKeys, reactQuery<PERSON>eys } from "src/renderer2/common";
import { dispatchRaygunError } from "../helper";

const useGetSecurityData = (cognitoUser, api: string) => {
  return useQuery(
    [reactQueryKeys.getSecurityData],
    async () => {
      try {
        const response = await axios.get(
          api ?? import.meta.env.VITE_API_SERVICE + '/reference-data/getSecurityToken'
        );

        if (response.data && response.data.data) {
          if (
            typeof response.data.data === "object" &&
            "err_message" in response.data.data
          ) {
            throw new Error(response.data.data.err_message);
          } else {
            return response.data.data;
          }
        } else {
          return null;
        }
      } catch (error) {
        throw new Error(error?.message ?? error);
      }
    },
    {
      enabled: !!cognitoUser && navigator.onLine,
      retry: false,
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      staleTime: 0,
      onError(error:any) {
        console.error("SECURITY TOKEN ERROR",error)
        const store = useGlobalStore.getState();
        dispatchRaygunError(
          error,
          [raygunKeys.failedToGetSecurityHash.tag]
        );
        if (error.code === 'ERR_NETWORK_CHANGED' || error.code === "ERR_NETWORK" || error.code === 'ECONNABORTED' || error.message.includes('Network')) {
            store.setApiFailureDueToNoInternet(true);
          }else{
          return error
        }
      }
    }
  );
};

export default useGetSecurityData;
