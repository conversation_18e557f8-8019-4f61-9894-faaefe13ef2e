import { useEffect, useRef, useState } from "react";
import { localStorageKeys, MinSearchDataLen, options, routes, units } from "../../../common";
import { useDebouncedValue } from "@mantine/hooks";
import axios from "axios";
import { commomKeys, getValidSearchData, searchProducts, useBuyerSettingStore, useGlobalStore, useSearchStore } from '@bryzos/giss-ui-library';
import styles from './productSearch.module.scss';
import { ReactComponent as SearchIcon } from '../../../assets/New-images/Search.svg';
import { ReactComponent as DropdownArrow } from '../../../assets/New-images/New-Image-latest/Polygon.svg';
import clsx from "clsx";
import { MenuItem, Select } from "@mui/material";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { convertUtcToLocalTime, fetchPrice, formatDisplayDateForTemplate, formatOrderSizeToDisplayText, newPriceFormatter, setLocal, updateSelectedPriceSearchData } from "src/renderer2/helper";
import usePostVerifyZipCode from "src/renderer2/hooks/usePostVerifyZipCode";
import useDialogStore from "src/renderer2/component/DialogPopup/DialogStore";
import { useLocation } from "react-router-dom";
import { useLeftPanelStore } from "src/renderer2/component/LeftPanel/LeftPanelStore";
import usePostSaveSearchProducts from "src/renderer2/hooks/usePostSaveSearchProducts";
dayjs.extend(customParseFormat);
dayjs.extend(utc);
dayjs.extend(timezone);

type ProductSearchState = {
};

function updateProductTitle(title: string): string {

  const endsWithUnderscoreNumber = /_(\d+)$/.exec(title);
  
  if (endsWithUnderscoreNumber) {
      const number = parseInt(endsWithUnderscoreNumber[1]);
      return title.replace(/_(\d+)$/, `_${number + 1}`);
  }
  
  return `${title}_2`;
}

const ProductSearch: React.FC<ProductSearchState> = ({}) => {
    const { shortListedSearchProductsData, searchByProductResult, setSearchByProductResult, searchString, setSearchString, searchZipCode, setSearchZipCode, sessionId, enableRejectSearchAnalytic, setEnableRejectSearchAnalytic, setSelectedSavedSearch, setSavedSearchProducts, savedSearchProducts } = useSearchStore();
    const productData: any = useGlobalStore(state => state.productData);
    const referenceData: any = useGlobalStore(state => state.referenceData);
    const setOrderSizeSliderValue = useSearchStore(state => state.setOrderSizeSliderValue);
    const orderSizeSliderValue = useSearchStore(state => state.orderSizeSliderValue);
    const selectedDomesticOption = useSearchStore(state => state.selectedDomesticOption);
    const setSelectedDomesticOption = useSearchStore(state => state.setSelectedDomesticOption);
    const selectedPriceUnit = useSearchStore(state => state.selectedPriceUnit);
    const setSelectedPriceUnit = useSearchStore(state => state.setSelectedPriceUnit);
    const selectedSavedSearch = useSearchStore(state => state.selectedSavedSearch);
    const [debouncedSearchData] = useDebouncedValue(searchString, 400);
    const [placeholder, setPlaceholder] = useState('Search by Product');
    const [orderSizeList, setOrderSizeList] = useState<any[]>([]);
    const searchProductInputRef = useRef<any>(null);
    const {buyerSetting} = useBuyerSettingStore();
    const { mutateAsync: verifyZipCode } = usePostVerifyZipCode();
    const { showCommonDialog, resetDialogStore } = useDialogStore();
    const [isFirstTimeFocus, setIsFirstTimeFocus] = useState(true);
    const location = useLocation();
    const { setClickedCreateNewButton } = useLeftPanelStore();
    const { mutateAsync: saveSearchProductsMutation } =
    usePostSaveSearchProducts();
    const isBuyerDeletedItemsPage = location.pathname === routes.buyerDeleteOrderPage;

    const shoudSearchInputBeDisabled = isBuyerDeletedItemsPage
useEffect(()=>{
    if(referenceData?.ref_weight_price_brackets?.length > 0){
        setOrderSizeList(referenceData?.ref_weight_price_brackets)
        setOrderSizeSliderValue(referenceData?.ref_weight_price_brackets.find((item: any) => item?.default_bracket)?.min_weight ?? Number(referenceData?.ref_weight_price_brackets[2].min_weight))
    }
},[referenceData])
    useEffect(()=>{
        if(buyerSetting?.price_search_zip){
            setTimeout(()=>{
                setSearchZipCode(buyerSetting.price_search_zip);
            }, 10)
        }
    },[buyerSetting])

    const initialPlaceholder = 'Search by Product';
    const secondPlaceholder = 'Search by Product';
    const focusPlaceholder = 'Example: Beam 12" x...';

    useEffect(()=>{
        if(searchByProductResult.length === 0 && searchProductInputRef.current && !document.activeElement?.dataset?.globalSearch){
            searchProductInputRef.current.focus();
        }
    },[searchByProductResult])

    const handleFocus = () => {
        setPlaceholder(focusPlaceholder);
    };

    const handleBlur = () => {
        setPlaceholder(shortListedSearchProductsData.length === 0 ? initialPlaceholder : secondPlaceholder);
        setIsFirstTimeFocus(false);
    };

    const handleDuplicateExpiredSearch = async () => {
      try {
        setClickedCreateNewButton(Math.random());
    
        const productsPriceData = await fetchPrice(
          selectedSavedSearch?.products,
          selectedSavedSearch?.zipcode,
          selectedSavedSearch?.order_size
        );
    
        const payload = {
          data: {
            id: undefined,
            title: updateProductTitle(selectedSavedSearch?.title),
            zipcode: selectedSavedSearch?.zipcode.trim(),
            order_size: String(selectedSavedSearch?.order_size),
            source: "search",
            products: selectedSavedSearch?.products,
          },
        };
    
        const response = await saveSearchProductsMutation(payload as any);
        const id = response?.data?.data;
    
        const updatedSelectedSavedSearch = {
          id,
          title: updateProductTitle(selectedSavedSearch?.title) || "Untitled",
          zipcode: selectedSavedSearch?.zipcode || searchZipCode,
          order_size: String(selectedSavedSearch?.order_size || orderSizeSliderValue),
          source: "search",
          products: (productsPriceData ?? []).map((product: any) => ({
            product_id: product.id,
            product_description: product.UI_Description,
            price: newPriceFormatter(product),
            price_unit: selectedPriceUnit.toLowerCase(),
            domestic_material_only: product.domestic_material_only,
          })),
          item_count: productsPriceData.length,
          time_stamp: dayjs().utc().format("YYYY-MM-DD HH:mm:ss"),
          created_date: dayjs().utc().format("YYYY-MM-DD HH:mm:ss"),
          search_date_time: dayjs()
            .tz("America/Chicago")
            .format("M/D/YY @ hh:mma [CT]"),
          pricing_expired: false,
        };
    
        setSelectedSavedSearch(updatedSelectedSavedSearch);
    
        setSavedSearchProducts([updatedSelectedSavedSearch, ...savedSearchProducts]);
    
        setLocal(localStorageKeys.instantPriceSearch, updatedSelectedSavedSearch);
      } catch (error) {
        console.error("Error in handleDuplicateExpiredSearch:", error);
      }
    };

    useEffect(() => {
        if (debouncedSearchData?.length >= MinSearchDataLen) {
            searchAnalyticsApi(sessionId, null, debouncedSearchData)
        }
    }, [debouncedSearchData])

    useEffect(() => {
        if (sessionId) {
            if (searchString.length === 1) {
                setEnableRejectSearchAnalytic(true)
            }
            if (searchString.length === 0 && searchByProductResult.length === 0 && debouncedSearchData && enableRejectSearchAnalytic) {
                searchAnalyticsApi(sessionId, 'Reject', debouncedSearchData)
            } else if (debouncedSearchData) {
                searchAnalyticsApi(sessionId, 'Accept', debouncedSearchData)
            }
        }
    }, [searchString])

    const searchHandler = (text: string) => {
        setSearchString(text);
        handleProductSearch(text);
    }

    const handleProductSearch = (searchString: string) => {
        const searchStrings = getValidSearchData(searchString);
        const productSearch = searchProducts(productData, searchStrings, searchString, undefined, false);
        const filterProductSearch = productSearch.filter((product) => {
            if (shortListedSearchProductsData.length !== 0 && shortListedSearchProductsData.some((data) => data.id === product.Product_ID)) {
                return false;
            } else {
                return true;
            }
        })
        setSearchByProductResult(filterProductSearch);

    }

    const searchAnalyticsApi = (sessionId: string, status: string | null, searchKeyword: string) => {
        const payload = {
            "data": {
                "session_id": sessionId,
                "keyword": searchKeyword,
                "status": status,
                "source": 'search'
            }
        }
        axios.post(import.meta.env.VITE_API_SERVICE + '/user/create_po_search', payload)
            .catch(err => console.error(err))
    }

    const handleZipCodeBlur = async (e: any) => {
        if(e.target.value.length === 5){
            const isValidZipCode = await verifyZipCode({zip_code: e.target.value});
        if(!isValidZipCode){
            showCommonDialog(null, "Invalid Zip Code", commomKeys.actionStatus.error, resetDialogStore, [{ name: commomKeys.errorBtnTitle, action: resetDialogStore }])
        }
        }
    }

    return (
        <>
        {selectedSavedSearch?.pricing_expired ? (
            <div className={styles.savedSearchExpired}>
                <div className={styles.savedSearchExpiredDetails}>
                    <span>Price List Name: <span className={styles.savedSearchExpiredDetailsSpan}>{selectedSavedSearch.title}</span></span>
                    <span>Order Size: <span className={styles.savedSearchExpiredDetailsSpan}>{selectedSavedSearch?.order_size ? formatOrderSizeToDisplayText(orderSizeList, Number(selectedSavedSearch?.order_size)) : '-'}</span></span>
                    <span>Destination Zip Code: <span className={styles.savedSearchExpiredDetailsSpan}>{selectedSavedSearch.zipcode}</span></span>
                </div>
               <div className={styles.savedSearchExpiredDetails1Container}>
               <div className={styles.savedSearchExpiredDetails1}>
                    <span className={styles.savedSearchExpiredDetailsSpan1}>SESSION ENDED</span>
                    <span className={styles.lastCreatedDate}>LIST CREATE DATE:</span>
                    <span className={styles.lastCreatedDateSpan}>{formatDisplayDateForTemplate(convertUtcToLocalTime(selectedSavedSearch?.created_date) , "ddd, MMM DD, YYYY hh:mmA")}</span>
                </div>
                {!Boolean(selectedSavedSearch?.is_deleted) && <button className={styles.updatePricingButton} onClick={handleDuplicateExpiredSearch}>Update pricing</button>}
               </div>
            </div>
        ) : (
            <>
                <div className={styles.searchFilterSection}>
                    <div
                        className={styles.zipBox}
                        data-hover-video-id='search-zip'
                    >
                        <span className={styles.destZIP}>Dest. Zip Code</span>
                        <span className={styles.zipCode}>
                            <input
                                type="text"
                                value={searchZipCode}
                                onChange={(e) => {
                                    e.target.value = e.target.value.replace(/\D/g, '');
                                    setSearchZipCode(e.target.value)
                                }}
                                onBlur={handleZipCodeBlur}
                                maxLength={5}
                                className={styles.zipInput}
                                disabled={isBuyerDeletedItemsPage}
                            />
                        </span>
                    </div>
                    <div data-hover-video-id='order-size-on-search' className={styles.dropdownBox}>
                        <span className={styles.dropdownLabel}>Order Size (LB)</span>
                        <Select
                            value={orderSizeSliderValue}
                            onChange={(event: any) => {
                                setOrderSizeSliderValue(Number(event.target.value))
                                setTimeout(()=>{
                                    updateSelectedPriceSearchData(shortListedSearchProductsData);
                                }, 0)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            disabled={isBuyerDeletedItemsPage}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                        >   
                        
                            {orderSizeList.map((item: any) => (
                                <MenuItem key={item.min_weight} value={Number(item.min_weight)}>
                                    {Number(item.min_weight) === Number(orderSizeList[orderSizeList.length - 1].min_weight) ? Number(item.min_weight).toLocaleString() + '+' : `${Number(item.min_weight).toLocaleString()} to ${Number(item.max_weight).toLocaleString()}`}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                    <div className={styles.dropdownBox} data-hover-video-id='pricing-unit-on-search'>
                        <span className={styles.dropdownLabel}>Pricing Units</span>
                        <Select
                            value={selectedPriceUnit}
                            onChange={(event: any) => {
                                setSelectedPriceUnit(event.target.value as string)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                            disabled={isBuyerDeletedItemsPage}
                        >
                            {units.map((item: any) => (
                                <MenuItem key={item.value} value={item.value}>
                                    {item.title}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                    <div className={styles.dropdownBox} data-hover-video-id='domestic-only-search'>
                        <span className={styles.dropdownLabel}>Domestic Required</span>
                        <Select
                            value={selectedDomesticOption}
                            onChange={(event: any) => {
                                setSelectedDomesticOption(event.target.value as boolean)
                            }}
                            className={clsx('productSearchDropdown', styles.dropdownValue)}
                            IconComponent={DropdownArrow}
                            MenuProps={
                                {
                                    classes: {
                                        paper: styles.dropDownBG
                                    },
                                }
                            }
                            disabled={isBuyerDeletedItemsPage}
                        >
                            {options.map((item: any) => (
                                <MenuItem key={item.value} value={item.value}>
                                    {item.title}
                                </MenuItem>
                            ))}
                        </Select>
                    </div>
                </div>
                <div className={styles.searchSection} data-disabled={shoudSearchInputBeDisabled}>
                    <div className={clsx(searchString.length > 0 ? styles.searchInputStartTyping : "", styles.searchBox, isFirstTimeFocus ? styles.firstTimeFocusBg : "")}
                        
                        data-hover-video-id='price-search'
                    >
                        <SearchIcon />
                        <input disabled={shoudSearchInputBeDisabled} placeholder={placeholder} onFocus={handleFocus} onBlur={handleBlur} value={searchString} onChange={event => { searchHandler(event.target.value); setIsFirstTimeFocus(false) }} ref={searchProductInputRef} />
                    </div>
                </div>
            </>
        )}

        </>
    )
}

export default ProductSearch;