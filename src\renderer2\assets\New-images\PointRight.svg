<svg width="26" height="39" viewBox="0 0 26 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_104_1275)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0.762624 31C3.16573 31 5.42729 29.9043 7.00374 28.0905C11.7222 22.6618 15.5099 19.5403 22 15.5C15.1146 11.096 11.4469 7.89728 6.98947 2.84061C5.42475 1.06554 3.19317 0.00370779 0.826896 0.00248807L-4 0L-4 31H0.762624ZM3.68991 24.9407L13.283 15.5L3.68864 6.0607C2.66011 5.0488 1.29179 4.48368 -0.131615 4.48295L-3.99923 4.48096V26.519H-0.133683C1.29095 26.519 2.66064 25.9537 3.68991 24.9407Z" fill="#FFB800"/>
</g>
<defs>
<filter id="filter0_d_104_1275" x="-8" y="0" width="34" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_104_1275"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_104_1275" result="shape"/>
</filter>
</defs>
</svg>
