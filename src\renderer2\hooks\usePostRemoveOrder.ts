
import { useMutation } from "@tanstack/react-query";
import axios from "axios";

const usePostRemoveOrder = () => {

  return useMutation(async (poNumber: string) => {
    try {
      const url = `${import.meta.env.VITE_API_ORDER_SERVICE}/buyer/${poNumber}/edit-mode`;
      const response = await axios.post(
        url
      );
      console.log("data @>>>>>>>", response.data);

     return response.data;
    } catch (error: any) {
      throw new Error(error?.message);
    }
  });
};

export default usePostRemoveOrder;
