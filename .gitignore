# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/scripts/nodeUploader/node_modules

/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

bryzoscert.pfx

npm-debug.log*
yarn-debug.log*
yarn-error.log*

/out

/.webpack

/src/main/config.js


stats.html
.env.production
.env.demo   

config-electron/config.production.json
config-electron/config.demo.json

package-lock.json