<!DOCTYPE html>
<html>

<head>
  <title>No Internet Access</title>
  <link href="https://fonts.googleapis.com/css?family=Syncopate:300,400,500,600,700,900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap"
    rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      -webkit-user-select: none;
      /* Safari */
      -ms-user-select: none;
      /* IE 10 and IE 11 */
      user-select: none;
      /* Standard syntax */
      font-family: 'Inter', sans-serif;
    }

    html,
    body {
      /* overflow: hidden;
      border-radius: 16px; */
      height: 100%;
      width: 100%;
      overflow: hidden;
    }

    .app-wrapper {
      width: 100%;
      height: 100%;
    }

    .wrapper-no-internet {
      width: 100%;
      height: 100%;
    }
    .no-internet-header {
      height: 8.59%;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 32px;
      padding-right: 32px;
      background-color: #0f0f14;
      position: relative;
      box-shadow: 0 5.5px 4.4px -3px rgba(0, 0, 0, 0.91);
      overflow: hidden;
    }

    .bgEllips {
      position: absolute;
      top: -70px;
      left: 25px;
      width: 225px;
      height: 55px;
      filter: blur(58px);
      -webkit-filter: blur(58px);
      background-image: conic-gradient(from 0.32turn, #fff, rgba(151, 134, 255, 0.59) 0.99turn, #fff 0.09turn, rgba(151, 134, 255, 0.1) 0.53turn, #fff 0.8turn, rgba(151, 134, 255, 0) 0.31turn, #fff), linear-gradient(348deg, #e7ecef 210%, rgba(231, 236, 239, 0.4) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0), rgba(151, 134, 255, 0.4));
    }

    .bgEllips1 {
      position: absolute;
      z-index: 1;
      bottom: -60px;
      right: 23px;
      width: 78px;
      height: 32.5px;
      filter: blur(26px);
      -webkit-filter: blur(26px);
      background-image: conic-gradient(from 0.42turn, #fff, rgba(151, 134, 255, 0.37) 0.89turn, #fff 0.09turn, rgba(151, 134, 255, 0.02) 0.83turn, #fff 0.51turn, rgba(151, 134, 255, 0.6) 0.36turn, #fff), linear-gradient(274deg, #e7ecef 210%, rgba(231, 236, 239, 0.4) 165%), linear-gradient(to bottom, rgba(151, 134, 255, 0.19), rgba(151, 134, 255, 0.4));
    }

    .container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-image: linear-gradient(to bottom, #0f0f14, #0f0f14), linear-gradient(179deg, #fff -12%, #1a1b21 28%);
      height: calc(100vh - 8.59%);
      width: 100%;
    }
    .no_internet_container{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 560px;
      height: 563px;
      background-image: url('./noInternetBackground.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
      padding: 60px 15px 40px 15px;
    }

    .no_internet_img {
      width: 104px;
      height: 104px;
      margin: 0 0 24px 0;
      object-fit: contain;
    }

    .try_again_btn {
      gap: 8px;
      padding: 14px 12px;
      border-radius: 10px;
      background-color: #fff;
      border: none;
      outline: none;
      font-family: Syncopate;
      font-size: 18px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.72px;
      text-align: center;
      color: #0f0f14;
      cursor: pointer;
      margin-top: 40px;
      width: 220px;
      height: 50px;
    }

    .content {
      font-family: Inter;
      font-size: 18px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: rgba(255, 255, 255, 0.8);
      margin-top: 8px;
    }

    .header {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.4;
      letter-spacing: -0.96px;
      text-align: left;
      color: #fff;
      /* -webkit-app-region: drag; */
    }

    .no-internet-btn {
      position: absolute;
      top: 1.3vw;
      right: 3vw;
      z-index: 2;
    }

    .no-internet-btn button {
      background-color: transparent;
      border: 0px;
      box-shadow: none;
      display: inline-block;
      transition: all 0.1s;
      cursor: pointer;
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button img {
      width: 4vw;
      height: 4vw;
    }

    .no-internet-btn button:hover {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
    }

    .drag-panel {
      -webkit-app-region: drag;
      width: 100%;
      height: 5vw;
      display: inline-flex;
      position: absolute;
      left: 1px;
      top: -12px;
    }

    .btn {
      cursor: pointer;
    }

    .closeAndMinimizeBtn {
      display: flex;
      flex-direction: row-reverse;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: -17px;
      right: -21px;
      z-index: 999999;
    }

    .macCloseAndMinimizeBtn {
      left: -20px;
      right: unset;
      flex-direction: row;
    }
    .bryzos_name{
      font-family: Syncopate;
      font-size: 28px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1;
      letter-spacing: -1.12px;
      text-align: left;
      color: #fff;
      padding-bottom: 58px;
    }
    .button-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }
    .mac-title-bar {
      width: 100%;
      height: 32px;
      padding: 2px 1384px 2px 8px;
      background-color: #191a20;
      display: flex;
      -webkit-app-region: drag;
    }
    .title-container {
      flex: 1;
      position: relative;
    }
    .minimize-btn svg:hover path {
      stroke: #fff;
    }
    .minimize-btn svg:hover rect {
      stroke-opacity: 1;
    }
    .cross-btn svg:hover path {
      stroke: #fff;
    }
    .cross-btn svg:hover rect {
      fill:#C42B1C;
    }
    body:not(.isMacDevice) .mac-title-bar {
      display: none;
    }
    
    body.isMacDevice .button-container {
      display: none;
    }
  </style>
</head>

<body class="no-internet-body">
  <div class="wrapper-no-internet">
    <div class="app-wrapper">
      <div class="mac-title-bar" ></div>
      <div class="no-internet-header">
        <div class="title-container">
          <span class="drag-panel"></span>
          <img src="./Logo.svg" alt="Logo" />
        </div>
        <div class="button-container">
          <div class="btn minimize-btn" id="minimize-app-button"> 
            <svg width="36" height="38" viewBox="0 0 36 38" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="0.25" y="1.1875" width="35.5" height="35.5" rx="3.75" stroke="#9B9EAC" stroke-opacity="0.5" stroke-width="0.5"/>
              <path d="M24 19.0625H12" stroke="#9B9EAC"/>
              </svg>
          </div>
        <div class="btn cross-btn" id="close-button">
          <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.25" y="0.25" width="35.5" height="35.5" rx="3.75" stroke="#9B9EAC" stroke-opacity="0.5" stroke-width="0.5"/>
            <path d="M13 13L23 23" stroke="#9B9EAC" stroke-width="1.5" stroke-linecap="round"/>
            <path d="M23.0017 13L13.0017 23" stroke="#9B9EAC" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            
        </div>
        </div>
        <div class="bgEllips"></div>
        <div class="bgEllips1"></div>
      </div>
      <div class="container">
        <div class="no_internet_container">
          <div class="bryzos_name">BRYZOS</div>
          <img class="no_internet_img" src="./No_Internet_Connection.svg" alt="No Internet Connection" />
          <p class="header">NO INTERNET CONNECTION</p>
          <p class="content">Please check your internet connection</p>
          <button class="try_again_btn" id="try-again-button">TRY AGAIN</button>
        </div>
      </div>

    </div>
    
  </div>
  <script>
    let isMacDevice = false;

    // Check if electron is available and get system info
    if (window.electron) {
      const os = window.electron.sendSync({ channel: 'systemVersion' });
      console.log("os", os);
      isMacDevice = os.includes('Mac');

      // Add Mac-specific class to closeAndMinimizeBtn
      if (isMacDevice) {
        const closeAndMinimizeBtn = document.querySelector('.closeAndMinimizeBtn');
        if (closeAndMinimizeBtn) {
          closeAndMinimizeBtn.classList.add('macCloseAndMinimizeBtn');
        }
      }
    }

    const tryAgainButton = document.getElementById('try-again-button');
    tryAgainButton.addEventListener('click', () => {
      // Notify the main process to reload the window
      console.log("window.electron", window.electron);
      if (window.electron)
        window.electron.send({ channel: 'reload-window' });
    });

    const minimizeAppButton = document.getElementById('minimize-app-button');
    minimizeAppButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowMinimize' });
    });

    const closeButton = document.getElementById('close-button');
    closeButton.addEventListener('click', () => {
      if (window.electron)
        window.electron.send({ channel: 'windowClose' });
    });

    // Add class to body based on OS
    if (isMacDevice) {
      document.body.classList.add('isMacDevice');
    }
  </script>
</body>

</html>