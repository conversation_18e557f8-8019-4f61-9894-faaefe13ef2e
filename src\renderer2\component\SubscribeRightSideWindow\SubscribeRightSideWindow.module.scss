.RightWindowcontainer{
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    height: 100%;
}
.contentContainer1 {
    width: 322px;
    height: 50%;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    padding: 24px 16px 16px;
    background-color: #191a20;

    .content {
        background-color: rgba(255, 255, 255, 0.04);
        width: 282px;
        height: calc(100% - 75px);
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        padding: 16px 16px 16px 16px;
        background-color: rgba(255, 255, 255, 0.04);

        .featureItem {
            display: flex;
            gap: 5px;
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            font-stretch: normal;
            font-style: normal;
            line-height: normal;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            .featureItemIcon{
                padding-left: 6px;
            }
            &:last-child{
                .featureItemText{
                    line-height: 1.4;
                }
            }
        }
    }
}

.title {
    font-family: Syncopate;
    font-size: 18px;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.2;
    letter-spacing: 0.72px;
    text-align: center;
    color: #fff;
    margin-bottom: 20px;
}

.contentContainer2 {
    width: 322px;
    height: 50%;
    background-origin: border-box;
    position: relative;
    overflow: hidden;
    padding: 24px 16px 16px;
    background-color: #191a20;

    .content {
        height:calc(100% - 70px);
        max-height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        &::-webkit-scrollbar {
            width: 5px;
            height: 6px;
        }
        .contentItem {
            // background: url(../../assets/New-images/Subscription-Right-Window-Container.svg) no-repeat transparent;
            width: 282px;
            background-color: #191a20;
            margin-bottom: 20px;
            .contentItemTitle {
                display: flex;
                flex-direction: column;
                gap: 23px;
                padding: 16px;
                background-color: rgba(255, 255, 255, 0.04);
                // background: url(../../assets/New-images/Subscription-Right-Window-Contain.svg) no-repeat transparent;
                .contentItemTitleDiv {
                    display: flex;
                    flex-direction: column;
                    .contentItemTitleSpan {
                        font-family: Syncopate;
                        font-size: 16px;
                        font-weight: bold;
                        font-stretch: normal;
                        font-style: normal;
                        line-height: 1.2;
                        letter-spacing: 0.64px;
                        text-align: left;
                        color: #fff;
                    }
                    .contentItemTitleSpanSmall {
                        font-family: Inter;
                        font-size: 16px;
                        font-weight: 300;
                        font-stretch: normal;
                        font-style: normal;
                        line-height: 1.2;
                        letter-spacing: 2.72px;
                        text-align: left;
                        color: #ff9d00;
                    }
                }
                .contentItemContent {
                    font-family: Inter;
                    font-size: 14px;
                    font-weight: 200;
                    font-stretch: normal;
                    font-style: normal;
                    line-height: 1.6;
                    letter-spacing: 0.56px;
                    text-align: left;
                    color: #fff;
                    .italicPara1.italicPara1{
                        font-weight: 300;
                        font-style: italic;
                    }

                    .italicPara2{
                        font-weight: normal;
                        font-style: italic;
                    }
                }
            }
        }
    }
}