.textNotificationNumberPopupDialog {

    .dialogContent {
      border-radius: 50px;
      background-color: #0f0f14;
      overflow: hidden;
      max-width: 789px;
      box-shadow: none;
      min-width: 500px;
      min-height: 500px;
    }
  
    .closeIcon {
      position: absolute;
      top: 24px;
      right: 24px;
    }
    .textNotificationNumberPopupWrapper{
        padding: 24px;
        font-family: Syncopate;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.3;
        letter-spacing: -0.72px;
        text-align: left;
        color: #71737f;
        text-transform: uppercase;
        .inputCreateAccount {
            width: 100%;
            height: 40px;
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
            align-items: center;
            position: relative;
            padding: 6px 16px;
            border-radius: 12px;
            background-color: rgba(255, 255, 255, 0.04);
            font-family: Inter;
            font-size: 14px;
            font-weight: normal;
            line-height: 1;
            letter-spacing: 0.56px;
            text-align: left;
            color: #fff;
            transition: background 0.1s;
            caret-color: #fff;
        
            &.companyNameInput {
              background: transparent;
            }
        
            &.arBryzosCom {
              color: #fff;
              cursor: not-allowed;
        
              &:focus {
                background: rgba(255, 255, 255, 0.04);
                color: #fff;
              }
            }
        
            &.sendInvoiceEmailInput {
              text-overflow: ellipsis;
            }
        
            &.inputError {
              box-shadow: inset 2.2px 2.2px 2.2px 0 #000;
              background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);
        
              &:focus {
                content: unset;
                box-shadow: none;
                background-image: linear-gradient(122deg, #720d16 -73%, #ff4859 67%), linear-gradient(356deg, #8c8b99 203%, #2f2e33 38%);
              }
            }
        
            &:focus {
              outline: none;
              color: #fff;
            }
        
            &.disableInput {
              color: #fff;
              cursor: not-allowed;
        
              &:focus {
                outline: none;
                color: #fff;
              }
            }
        
            &:disabled {
              cursor: not-allowed;
            }
          }
    }
  }