import clsx from 'clsx';
import { useRightWindowStore } from './RightWindowStore';
import styles from './rightWindow.module.scss';
import {
  getChannelWindow,
  useGlobalStore,
  useBuyerSettingStore,
  useSellerSettingStore,
} from '@bryzos/giss-ui-library';
import { useRef, useEffect } from 'react';
import { ReactComponent as AcrylicCorner } from '../../assets/New-images/corner3.svg';
import VideoPlayerRightWindow from './VideoPlayerRightWindow/VideoPlayerRightWindow';
import CompleteYourAccountSetup from '../buyer/CompleteYourAccountSetup/CompleteYourAccountSetup';
import CompleteYourAccountSetupSeller from '../seller/CompleteYourAccountSetup/CompleteYourAccountSetup';
import { routes, userRole } from 'src/renderer2/common';
import { getNestedValue, isValidValue } from 'src/renderer2/helper';
import { useLocation } from 'react-router-dom';

const requiredFieldsToCompleteAccountSetup = {
  company: [
    'company_type',
    'company_address.line1',
    'company_address.city',
    'company_address.state_code',
    'company_address.state_id',
    'company_address.zip',
    'ap_contact_name',
    'ap_email_id',
    'send_invoices_to',
  ], // all required
  user: ['first_name', 'last_name', 'email_id', 'phone', 'price_search_zip'],
  shipment: [ 'delivery_address',],
  resale: [  'resale_certificate',],
  payment: ['card' , 'bnpl_settings'], // one of these is required
};

const requiredFieldsToCompleteAccountSetupSeller = {
company: ['company_name', 'company_type',  'company_address.line1',
    'company_address.city',
    'company_address.state_code',
    'company_address.state_id',
    'company_address.zip', 'ar_contact_name', 'ar_email_id',],
    payment: ['funding_settings.bank_name', 'funding_settings.routing_number', 'funding_settings.account_number', 
        'remittance_email'],
        user: ['first_name', 'last_name', 'email_id', 'phone'],
        shipments: ['stocking_address.line1', 'stocking_address.city', 'stocking_address.state_id', 'stocking_address.zip']
}
  
  // --- main validator ---
  function validateAccountSetup(account: any) {
    const errors: Record<string, string[]> = {
      company: [],
      user: [],
      shipment: [],
      resale: [],
      payment: [],
    };
  
    // 1. validate company, user, shipment, resale (all required)
    for (const section of ['company', 'user', 'shipment', 'resale'] as const) {
      for (const field of requiredFieldsToCompleteAccountSetup[section]) {
        const value = getNestedValue(account, field);
        if (!isValidValue(value)) {
          errors[section].push(field);
        }
      }
    }
  
    // 2. validate payment (at least one valid)
    const paymentFields = requiredFieldsToCompleteAccountSetup.payment;
    const hasValidPayment = paymentFields.some((field) =>
      isValidValue(account?.[field])
    );
  
    if (!hasValidPayment) {
      errors.payment = [...paymentFields]; // mark all as missing
    }
  
    // --- result ---
    const isValid = Object.values(errors).every((arr) => arr.length === 0);
    return { isValid, errors };
  }


  function validateAccountSetupSeller(account: any) {
    const errors: Record<string, string[]> = {
      company: [],
      payment: [],
      user: [],
      shipments: [],
    };
  
    for (const section of Object.keys(requiredFieldsToCompleteAccountSetupSeller) as Array<
      keyof typeof requiredFieldsToCompleteAccountSetupSeller
    >) {
      for (const field of requiredFieldsToCompleteAccountSetupSeller[section]) {
        if (!field) continue; // skip empty string fields
        const value = getNestedValue(account, field);
        if (!isValidValue(value)) {
          errors[section].push(field);
        }
      }
    }
  
    const isValid = Object.values(errors).every((arr) => arr.length === 0);
    return { isValid, errors };
  }

const RightWindow = ({
  rightWindowRef,
  routerContentRef,
  updateBackdropOverlay,
}: {
  rightWindowRef: React.RefObject<HTMLDivElement>;
  routerContentRef: React.RefObject<HTMLDivElement>;
  updateBackdropOverlay: boolean;
}) => {
  const { userData } = useGlobalStore();
  const { buyerSetting } = useBuyerSettingStore();
  const { sellerSettings } = useSellerSettingStore();
  const isBuyer = userData?.data?.type === userRole.buyerUser;
  const isSeller = userData?.data?.type === userRole.sellerUser;
  const location = useLocation();
  const showCompleteAccountSetupRoutes = [routes.homePage, routes.quotePage, routes.createPoPage, routes.orderManagementPage]
  const showCompleteAccountSetupRoutesSeller = [routes.previewOrderPage, routes.orderPage, routes.orderManagementPage]
  const { loadComponent, toolTipVideoComponent, showVideo, setShowVideo } =
    useRightWindowStore();
  const channelWindow: any = getChannelWindow();
  const isSettingPage = location.pathname === routes.buyerSettingPage || location.pathname === routes.sellerSettingPage;

  const { isValid, errors } = validateAccountSetup(buyerSetting);
  const { isValid: isValidSeller, errors: errorsSeller } = validateAccountSetupSeller(sellerSettings);

  if ((!(isBuyer && !isValid) && !(isSeller && !isValidSeller) && !loadComponent && !toolTipVideoComponent) || isSettingPage) {
    return null;
  }
  return (
    <div className={styles.rightWindowContainer}>
      {updateBackdropOverlay && <div className='backdropOverlay' />}
      <div className={styles.rightWindow} ref={rightWindowRef}>
        <div className={clsx('wrapperOverlay2', styles.wrapperOverlay)}></div>
        {loadComponent}
        {isBuyer && !isValid && showCompleteAccountSetupRoutes.includes(location.pathname) && <CompleteYourAccountSetup errors={errors} />}
        {isSeller && !isValidSeller && showCompleteAccountSetupRoutesSeller.includes(location.pathname) && <CompleteYourAccountSetupSeller errors={errorsSeller} />}
        {/* {toolTipVideoComponent?toolTipVideoComponent:loadComponent} */}
        {/* {toolTipVideoComponent} */}
      </div>
    </div>
  );
};

export default RightWindow;
