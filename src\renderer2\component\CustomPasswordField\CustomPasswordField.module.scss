.togglePassWrapper {
    display: flex;

    .passwordMatchContainer {
        display: flex;
        align-items: center;
        gap: 3px;
        width: 100%;
        height: 100%;

        .passwordMatch {
            width: 7px;
            height: 7px;
            border-radius: 50%;
        }

        .match {
            background-color: #acff24;
        }

        .mismatch {
            background-color: #ff4859;
        }

        .extraChar {
            background-color: rgba(255, 255, 255, 0.5);
        }
    }

    .passwordInputContainer {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .passwordOverlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        font-family: Inter;
        // font-size: 15px;
        // letter-spacing: 0.6px;
        cursor: text;
        color: var(--W--01);
        pointer-events: none; // Allow clicks to pass through to the input

        &.focused {
            color: #fff; // Blue color when focused
            font-weight: normal;
            // Removed the blue placeholder style to keep it gray

            .maskedPassword, .plainPassword {
                color: #fff; // Blue text when focused
            }
        }

        .placeholder {
            color: #616575;
        }

        .maskedPassword {
            display: flex;
            align-items: center;
            font-size: 19px;

            .lastChar {
                margin-left: 1px;
                font-size: 14px; // preserve the size of the last character
            }
        }

        .plainPassword {
            display: flex;
            align-items: center;
        }
    }

    .hiddenInput {
        // Keep the input fully visible for cursor and selection
        opacity: 1;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 2; // Place above the overlay for better interaction

        // Critical styles to hide text but keep cursor visible
        color: transparent !important; // Hide the text
        -webkit-text-fill-color: transparent !important; // For WebKit browsers

        // Make cursor highly visible
        caret-color: #fff !important;
        background: transparent !important;

        // Ensure text remains invisible when selected
        &::selection {
            background-color: rgba(31, 187, 255, 0.5) !important; // More visible blue selection
            color: transparent !important;
            -webkit-text-fill-color: transparent !important;
        }
    }
}

.togglePassWrapperMain {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 50px;
    padding: 0px 15px 0px 24px;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid transparent;
    min-width: 256px;
      transition: all 200ms ease-in-out;

    &.inputErrorPass{
        border: 0px;
        box-shadow: var(--box-shadow-focus-error);
        background-image: linear-gradient(138deg, #720d16 -109%, #ff4859 87%), linear-gradient(328deg, #fff 16%, #2c2c2c -14%);
        background-origin: border-box;
        background-clip: border-box, border-box;
        font-weight: bold;
        &:focus-within{
          color: #fff;
          box-shadow: none;
        }
      }

      &.maskedInputErrorPass{
        border: 0px;
        box-shadow: var(--box-shadow-focus-error);
        background-image: linear-gradient(138deg, #720d16 -109%, #ff4859 87%), linear-gradient(328deg, #fff 16%, #2c2c2c -14%);
        background-origin: border-box;
        background-clip: border-box, border-box;
        font-weight: bold;
        &:focus-within{
          color: #fff;
          box-shadow: none;
        }
      }


    &:focus-within {
        outline: none;
        color: #fff;
        background: url(../../../renderer2/assets/New-images/emailInput.svg) ;
        background-repeat: no-repeat;
        background-position: bottom right;
        background-size: cover;
    }

    input {
        width: 100%;
        height: 100%;
        font-family: Inter;
        font-size: 18px;
        letter-spacing: 0.6px;
        text-align: left;
        outline: none;
        caret-color: #fff;
        color: var(--W--01);
        transition: all 0.1s;
        background-color: transparent;
        border: 0px;
        padding: 0px;

        &::placeholder {
            color: var(--placeholder);
        }

        &:focus {
            color: #fff;
        }


    }

}

.showPassBtn{
    display: flex;
}

.inputPass.inputPass{
    padding: 0px;
    background-color: transparent;
    background-image: unset;
    box-shadow: none;
    caret-color: #fff;
    &:focus{
        background-color: transparent;
        box-shadow: none;
        border: 0px;
    }
}
.eyeIcon {
    height: 20px;
    width: 20px;

    &:hover {
      .img1 {
        display: none;
      }

      .img2 {
        display: block;
      }
    }

    .img2 {
      display: none;
    }
}