.viewingPane {
  width: 100%;
  height: 100%;
  border-style: solid;
  border-width: 1px;
  border-image-source: linear-gradient(to left, #fff -5%, #1b1b21 1%);
  border-image-slice: 1;
  background-color: #191a20;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  
}

.placeholderContent {
  text-align: center;
  z-index: 1;
  position: relative;
  padding: 20px;
  
  // Optional: Add a subtle animation for the placeholder
  animation: fadeInUp 0.6s ease-out;
  font-family: Inter;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.43;
  letter-spacing: 1.12px;
  text-align: center;
  color: #fff;
}

.justMissedPopup {
  .dialogContent {
    width: 100%;
    max-width: 555px;
    padding: 56px 37px 56px 37px;
    border-radius: 50px;
    background-color: #0f0f14;
    box-shadow: none;

    .closeIcon{
      position: absolute;
      top: 24px;
      right: 24px;
      cursor: pointer;
    }

    .youJustMissetext {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.96px;
      text-align: center;
      color: #fff;
      margin-bottom: 24px;
    }

    .thisOrderMissedtest {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #dbdcde;
      margin-bottom: 8px;
      display: block;
      &.thisOrderMissedtest1{
        margin: 4px 0px 12px 0px;
      }
    }

    .claimAnotherOrderbtn {
      margin-top: 32px;
      height: 60px;
      padding: 0px 17px;
      border-radius: 12px;
      background-image: linear-gradient(119deg, #1c40e7 -12%, #16b9ff 109%);
      font-family: Syncopate;
      font-size: 20px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.8px;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}


.justMissedPopup {
  .dialogContent {
    width: 100%;
    max-width: 555px;
    padding: 56px 37px 56px 37px;
    border-radius: 50px;
    background-color: #0f0f14;
    box-shadow: none;

    .closeIcon{
      position: absolute;
      top: 24px;
      right: 24px;
      cursor: pointer;
    }

    .youJustMissetext {
      font-family: Syncopate;
      font-size: 24px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.96px;
      text-align: center;
      color: #fff;
      margin-bottom: 24px;
    }

    .thisOrderMissedtest {
      font-family: Inter;
      font-size: 16px;
      font-weight: normal;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: normal;
      text-align: center;
      color: #dbdcde;
      margin-bottom: 8px;
      display: block;
      &.thisOrderMissedtest1{
        margin: 4px 0px 12px 0px;
      }
    }

    .claimAnotherOrderbtn {
      margin-top: 32px;
      height: 60px;
      padding: 0px 17px;
      border-radius: 12px;
      background-image: linear-gradient(119deg, #1c40e7 -12%, #16b9ff 109%);
      font-family: Syncopate;
      font-size: 20px;
      font-weight: bold;
      font-stretch: normal;
      font-style: normal;
      line-height: 1.3;
      letter-spacing: -0.8px;
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
