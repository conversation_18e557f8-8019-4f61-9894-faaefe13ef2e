// SearchField.tsx
import React, { InputHTMLAttributes, forwardRef } from "react";
import styles from "./VideoSearchField.module.scss";
import clsx from "clsx";
import { ReactComponent as SearchIcon } from "../../assets/New-images/Search.svg";

export interface VideoSearchFieldProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, "onChange"> {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const VideoSearchField = forwardRef<HTMLInputElement, VideoSearchFieldProps>(
  ({ value, onChange, placeholder = "Search Entire Video Library", className, ...rest }, ref) => {
    return (
      <div className={styles.searchFieldMain}>
      <label className={clsx(styles.wrapper, className)}>
        <span className={styles.icon} aria-hidden="true">
          {/* Inline magnifying glass so you don’t need an asset */}
          <SearchIcon />
        </span>
        <input
          ref={ref}
          className={styles.input}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          type="text"
          {...rest}
        />
      </label>
      </div>
    );
  }
);

export default VideoSearchField;
