// VideoLibraryPanel.tsx
// React + TypeScript + SCSS Modules (no Tailwind, no UI libs)
// Matches the slim dark sidebar UI from your screenshot and your latest requirements.
// - Section headers come from top-level JSON keys in order.
// - Only render items where show_on_ui === 1 and is_active === 1.
// - Sort by numeric `sequence` (desc) → `created_date` (desc) → `title` (asc).
// - Row label uses `title` (this is the "Episode #" text in mock).
// - Duration is hidden unless an item has a `duration` field (string or number).
// - Thumbnail: ALWAYS use `thumbnail_s3_url_map.thumbnail_app`; if missing, leave empty box (no fallback).
// - Play icon button is **disabled if share_video_url is empty**.
// - Row click always calls onItemClick even if video_s3_url is null/empty (no special handling).
// - Top "Create New Watchlist" button is present, visual only; it calls onCreateWatchlistClick if provided.
// - Collapsible sections are in-memory only (no localStorage).

import React, { useMemo, useState, memo, useEffect } from "react";
import { ReactComponent as ShareIcon } from "../../assets/New-images/New-Image-latest/share-outlined.svg";
import { ReactComponent as CreateNewPluseIconHover } from '../../assets/New-images/New-Image-latest/createNewPluseHover.svg';
import styles from "./VideoLibraryPanel.module.scss";
import { useVideoStore } from "./VideoStore";
import clsx from "clsx";

//"../../../assets/New-images/New-Image-latest/share-outlined.svg";

/************* Types *************/
export type ThumbnailMap = {
    intro_mobile?: string;
    intro_tablet?: string;
    intro_desktop?: string;
    thumbnail_app?: string;
    thumbnail_safe?: string;
    electron_player?: string;
    [k: string]: string | undefined;
};

export type VideoItem = {
    id: string;
    title: string;
    caption?: string | null;
    description?: string | null;
    video_s3_url?: string | null;
    thumbnail_s3_url_map?: ThumbnailMap | null;
    thumbnail_s3_url?: string | null; // unused for thumbnails per spec, but present in data
    subtitle_s3_url?: string | null;
    share_video_url?: string | null;
    show_on_ui: number; // 1 to show
    is_active: number; // 1 if active
    sequence?: string | null; // sortable, e.g. "26.00"
    created_date?: string | null; // ISO-like timestamp
    duration?: number | string | null; // optional; if present show as hh:mm:ss or string
    [k: string]: unknown;
};

export type LibraryData = Record<string, VideoItem[]>; // top-level keys are section names

export type VideoLibraryPanelProps = {
    // data: LibraryData; // pass raw.data from your payload
    // onItemClick?: (item: VideoItem, section: string) => void;
    // onShareClick?: (item: VideoItem, section: string) => void;
    // onCreateWatchlistClick?: () => void; // optional; no-op by default
    className?: string;
    style?: React.CSSProperties;
};

/************* Utils *************/
const cx = (...cn: Array<string | undefined | false>) => cn.filter(Boolean).join(" ");

const toNumberOrNull = (v?: string | null) => {
    if (v == null) return null;
    const n = parseFloat(v);
    return Number.isFinite(n) ? n : null;
};

const parseDateOrNull = (v?: string | null) => {
    if (!v) return null;
    const d = new Date(v.replace(" ", "T")); // tolerate "YYYY-MM-DD HH:mm:ss"
    return isNaN(d.getTime()) ? null : d;
};

const compareItems = (a: VideoItem, b: VideoItem) => {
    // primary: sequence (desc)
    const sa = toNumberOrNull(a.sequence);
    const sb = toNumberOrNull(b.sequence);
    if (sa != null || sb != null) {
        if ((sb ?? -Infinity) !== (sa ?? -Infinity)) return (sa ?? -Infinity) - (sb ?? -Infinity);
    }
    // secondary: created_date (desc)
    const da = parseDateOrNull(a.created_date)?.getTime() ?? -Infinity;
    const db = parseDateOrNull(b.created_date)?.getTime() ?? -Infinity;
    if (db !== da) return db - da;
    // tertiary: title (asc)
    return (a.title ?? "").localeCompare(b.title ?? "");
};

const hasDuration = (d: unknown): d is number | string => d !== undefined && d !== null && d !== "";

const formatDuration = (d: number | string) => {
    if (typeof d === "string") return d; // assume already formatted
    if (!Number.isFinite(d)) return "";
    const total = Math.max(0, Math.floor(d));
    const h = Math.floor(total / 3600);
    const m = Math.floor((total % 3600) / 60);
    const s = total % 60;
    const two = (n: number) => String(n).padStart(2, "0");
    return h > 0 ? `${h}:${two(m)}:${two(s)}` : `${m}:${two(s)}`;
};

/************* Icons (inline SVG, minimal) *************/
const Chevron = ({ open }: { open: boolean }) => (
    <svg className={cx(styles.icon, styles.chevron, open && styles.open)} width="12" height="9" viewBox="0 0 12 9" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M6.4147 8.38433C6.21652 8.67856 5.78349 8.67856 5.5853 8.38433L0.462761 0.779331C0.239058 0.447218 0.47703 0 0.877459 0L11.1225 0C11.523 0 11.7609 0.447217 11.5372 0.77933L6.4147 8.38433Z" fill="#9B9EAC"/>
</svg>
);

// const PlayIcon = ({ disabled }: { disabled?: boolean }) => (
//     <svg className={cx(styles.icon, styles.playIcon, disabled && styles.disabled)} width="16" height="16" viewBox="0 0 24 24" aria-hidden>
//         <path d="M8 5v14l11-7z" fill="currentColor" opacity={disabled ? 0.35 : 1} />
//     </svg>
// );

/************* Row Component *************/
const ItemRow = memo(function ItemRow({
    item,
    section,
    onItemClick,
    onShareClick,
}: {
    item: VideoItem;
    section: string;
    onItemClick?: (item: VideoItem, section: string) => void;
    onShareClick?: (item: VideoItem, section: string) => void;
}) {
    const thumbnail = item.thumbnail_s3_url_map?.thumbnail_app ?? ""; // no fallback per spec
    const duration = item.duration;
    const playDisabled = !item.share_video_url || String(item.share_video_url).trim() === "";
    const {selectedVideo} = useVideoStore();


    const handleRowClick = () => {
        onItemClick?.(item, section);
    };

    const handlePlayClick = (e: React.MouseEvent) => {
        e.stopPropagation();
       onShareClick?.(item, section);
    };

    return (
        <div className={clsx(styles.itemRow, selectedVideo.id === item.id && styles.selected)} role="button" tabIndex={0} onClick={handleRowClick} onKeyDown={(e) => (e.key === "Enter" || e.key === " ") && handleRowClick()}>
            <div className={styles.itemHeader}>
                <div className={styles.itemTitleLine}>{item.title}</div>
                {hasDuration(duration) && (
                    <div className={styles.duration}>{formatDuration(duration as number | string)} </div>
                )}
            </div>

            <div className={styles.itemBody}>
                <div className={cx(styles.thumb, !thumbnail && styles.thumbEmpty)}>
                    {thumbnail && <img src={thumbnail} alt="" loading="lazy" />}
                </div>
                <div className={styles.metaText}>
                    {/* Show caption if present; otherwise first non-empty of description */}
                    {item.caption?.trim() ? (
                        <div className={styles.primaryText}>{item.caption}</div>
                    ) : item.description?.trim() ? (
                        <div className={styles.primaryText}>{item.description}</div>
                    ) : (
                        <div className={cx(styles.primaryText, styles.muted)}>(No details)</div>
                    )}
                </div>
               {!playDisabled ?  <button
                    type="button"
                    className={cx(styles.iconBtn)}
                    onClick={handlePlayClick}
                >
                    <ShareIcon />
                </button> : null}
            </div>
        </div>
    );
});

/************* Section Component *************/
const Section = memo(function Section({
    name,
    items,
    onItemClick,
    onShareClick,
}: {
    name: string;
    items: VideoItem[];
    onItemClick?: (item: VideoItem, section: string) => void;
    onShareClick?: (item: VideoItem, section: string) => void;
}) {
    const [open, setOpen] = useState(true);
    const filteredSorted = useMemo(() => {
        return items
            .filter((it) => it.show_on_ui === 1 && it.is_active === 1)
            .sort(compareItems);
    }, [items]);

    return (
        <div className={styles.section}>
            <button
                type="button"
                className={styles.sectionHeader}
                onClick={() => setOpen((o) => !o)}
                aria-expanded={open}
                aria-controls={`section-${name}`}
            >
                <span className={styles.sectionTitle}>{name}</span>
                <Chevron open={open} />
            </button>

            {open && (
                <div id={`section-${name}`} className={styles.sectionList}>
                    {filteredSorted.length === 0 ? (
                        <div className={styles.empty}>No videos yet</div>
                    ) : (
                        filteredSorted.map((item) => (
                            <ItemRow
                                key={item.id}
                                item={item}
                                section={name}
                                onItemClick={onItemClick}
                                onShareClick={onShareClick}
                            />
                        ))
                    )}
                </div>
            )}
        </div>
    );
});

/************* Main Panel *************/
export default function VideoLibraryPanel({ className, style }: VideoLibraryPanelProps) {
    const [data, setData] = useState([]);
    const {panelData, setSelectedSection, setSelectedVideo,shareVideoObject, setShareVideoObject,transformedSelectedVideo, showPiP, videoSearchText } = useVideoStore();
    const sections = useMemo(() => Object.keys(data || {}), [data]);

    useEffect(() => {
        if(panelData && Object.keys(panelData).length > 0){
            setData(panelData);
            if(showPiP === true && transformedSelectedVideo){
                setSelectedVideo(transformedSelectedVideo);
                const section = Object.keys(panelData).find((key) => panelData[key].find((item) => item.id === transformedSelectedVideo.id));
                setSelectedSection(section);
            } else{
                setSelectedSection(Object.keys(panelData)[0]);
                setSelectedVideo(panelData[Object.keys(panelData)[0]][0]);
            }
        }
    }, [panelData]);

    useEffect(() => {
        if(videoSearchText){
            const filteredData = Object.keys(panelData).reduce((acc, key) => {
                acc[key] = panelData[key].filter((item) => item.title.toLowerCase().includes(videoSearchText.toLowerCase().trim()));
                return acc;
            }, {});
            setData(filteredData);
        } else{
            setData(panelData);
        }
    }, [videoSearchText]);


    return (
        <aside className={cx(styles.panel, className)} style={style}>
            <div className={styles.toolbar}>
                {/* <button type="button" className={styles.createBtn} onClick={
                    () => {
                        console.log("Create New Watchlist"); 
                    }
                } aria-label="Create New Watchlist">
                    <span className={styles.createText}>Create New Watchlist</span>
                    <span className={styles.createButtonIcon}><CreateNewPluseIconHover /></span>
                </button> */}
            </div>

            <div className={styles.libraryLabel}>VIDEO LIBRARY</div>

            <div className={styles.sectionsWrap}>
                {sections.map((name) => (
                    (data[name]&& data[name].length > 0) && (<Section key={name} name={name} items={data[name] || []} 
                    onItemClick={(videoObject, section)=>{
                        setSelectedSection(section);
                        setSelectedVideo(videoObject);
                    }} 
                    
                    onShareClick={(videoObject, section)=>{
                        setShareVideoObject({video_id: videoObject.id, share_video_url: videoObject.share_video_url});
                    }} />
                    )
                ))}
            </div>
        </aside>
    );
}
