import React, { useEffect, useState } from 'react'
import styles from './TextNotificationNumberPopup.module.scss'
import { Dialog } from '@mui/material';
import { formatPhoneNumber, formatPhoneNumberRemovingCountryCode, useBuyerSettingStore, useGlobalStore } from '@bryzos/giss-ui-library';
import { ReactComponent as CloseIcon } from '../../assets/New-images/close-icon.svg';
import InputWrapper from '../InputWrapper';
import CustomTextField from '../CustomTextField';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import CustomToggleCheckbox from '../CustomToggleCheckbox';
import clsx from 'clsx';
import { Auth } from 'aws-amplify';


const textNotificationNumberSchema = yup.object().shape({
    textNotificationNumber: yup.string().test('phone-digits', 'Phone number must have at least 10 digits', function (value) {
        if (!value) return true; // Let required validation handle empty values
        const digitCount = (value.match(/\d/g) || []).length;
        return digitCount >= 10;
    }).required('Phone number is required'),
    textNotificationOptIn: yup.boolean(),
    otp: yup.string().required('OTP is required'),


});

const TextNotificationNumberPopup = ({
    showTextNotificationNumberPopup,
    setShowTextNotificationNumberPopup,
    effectiveContainerRef,
    defaultTextNotificationNumber,
    defaultTextNotificationOptIn,
}: any) => {
    const { showLoader }: any = useGlobalStore();
    const {
        register,
        handleSubmit,
        clearErrors,
        setError,
        setValue,
        reset,
        watch,
        control,
        getValues,
        trigger,
        resetField,
        formState: { errors, dirtyFields, isDirty, isValid, isSubmitting },
        getFieldState,
    } = useForm({
        resolver: yupResolver(textNotificationNumberSchema),
        mode: 'onSubmit',
    });
    const [showOtpInput, setShowOtpInput] = useState(false);
    const {setShowLoader} = useGlobalStore();


    useEffect(() => {
        if(!showTextNotificationNumberPopup){
            reset();
            setShowOtpInput(false);
        }
    }, [showTextNotificationNumberPopup]);

    useEffect(() => {
        setValue(
            'textNotificationNumber',
            defaultTextNotificationNumber ? formatPhoneNumber(
                formatPhoneNumberRemovingCountryCode(defaultTextNotificationNumber)
            ) : ''
        );
        setValue('textNotificationOptIn', defaultTextNotificationOptIn);
    }, [defaultTextNotificationNumber, defaultTextNotificationOptIn]);

    async function updateAndSendPhoneVerification(phoneNumber: string) {
        try {
          const user = await Auth.currentAuthenticatedUser();
      
          // 1️⃣ Update the phone number in Cognito
          await Auth.updateUserAttributes(user, {
            phone_number: phoneNumber,
          });
          console.log("✅ Phone number updated (awaiting verification)");
      
          // 2️⃣ Immediately trigger OTP verification
          await Auth.verifyUserAttribute(user, "phone_number");
          console.log("📲 Verification code sent to", phoneNumber);
      
          return { success: true, message: "Verification code sent successfully." };
        } catch (error) {
          console.error("❌ Error updating or sending OTP:", error);
          return { success: false, error };
        }
      }

    // Step 2: Submit the verification code
    async function verifyOtp(code: string) {
        try {
          const user = await Auth.currentAuthenticatedUser();
          await Auth.verifyUserAttributeSubmit(user, "phone_number", code);
          console.log("Phone number verified successfully!");
        } catch (error) {
          console.error("Error verifying OTP:", error);
          // Optionally: rollback or ask user to retry
        }
      }

    const handleVerifyNumber = async () => {
        try {
            const isValid = await trigger('textNotificationNumber');
            if (isValid) {
                console.log('watch("textNotificationNumber")', watch('textNotificationNumber') , "+919082949835");
                setShowLoader(true);
                // const response = await updateAndSendPhoneVerification("+919082949835");
                // console.log('response', response);
                setShowOtpInput(true);
                setValue('otp', '');
            }
        } catch (error) {
            setShowOtpInput(false)
            setError('textNotificationNumber', { message: error?.message || 'Something went wrong' });
            console.error('Error verifying number:', error);
        }
        finally{
            setShowLoader(false);
        }
    }

    const handleVerifyOtp = async () => {
        try {
            setShowLoader(true);
            const isValid = await trigger('otp');
            if (isValid) {
                // const response = await verifyOtp(watch('otp'));
                // console.log('response', response);
                console.log('OTP is valid');
            }
        } catch (error) {
            setError('otp', { message: error?.message || 'Something went wrong' });
            setShowOtpInput(false)
            console.error('Error verifying OTP:', error);
        }
        finally{
            setShowLoader(false);
        }
    }

    return (
        <div className={styles.textNotificationNumberPopup}>
            <Dialog
                open={showTextNotificationNumberPopup}
                onClose={() => setShowTextNotificationNumberPopup(false)}
                transitionDuration={100}
                container={effectiveContainerRef.current}
                disableScrollLock={true}
                style={{
                    position: 'absolute',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backdropFilter: 'blur(7px)',
                    WebkitBackdropFilter: 'blur(7px)',
                    backgroundColor: 'rgba(0, 0, 0, 0.23)',
                    border: '1px solid transparent',
                    borderRadius: '0px 0px 10px 10px',
                    opacity: showLoader ? 0 : 1
                }}
                PaperProps={{
                    style: {
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                        margin: 0,
                    },
                }}
                hideBackdrop
                classes={{
                    root: styles.textNotificationNumberPopupDialog,
                    paper: styles.dialogContent,
                }}
            >
                <button
                    className={styles.closeIcon}
                    onClick={() => setShowTextNotificationNumberPopup(false)}
                >
                    <CloseIcon />
                </button>

                <div className={styles.textNotificationNumberPopupWrapper}>
                    <form>
                        <div className={styles.inputGroup}>
                            <div>
                                <span> TEXT NOTIFICATION OPT-IN </span>
                                <span>
                                    <CustomToggleCheckbox
                                        name="textNotificationOptIn"
                                        control={control}
                                        onChange={
                                            (e: any) => {
                                                setValue('textNotificationOptIn', e);
                                            }
                                        }
                                    />
                                </span>
                            </div>
                            <div className={styles.instructionText}>YOUR MOBILE NUMBER</div>
                            <div className={styles.inputMainOtp}>
                                <InputWrapper>
                                    <CustomTextField
                                        className={clsx(
                                            styles.inputCreateAccount,
                                            errors?.textNotificationNumber && styles.error
                                        )}
                                        type='tel'
                                        register={register('textNotificationNumber')}
                                        placeholder='(*************'
                                        errorInput={errors?.textNotificationNumber}
                                        mode='phoneNumber'
                                        disabled={showOtpInput}
                                    />
                                </InputWrapper>
                                <button type="button" onClick={handleVerifyNumber}> {showOtpInput ? 'RESEND OTP' : 'VERIFY NUMBER'} </button>
                            </div>

                            {showOtpInput && (
                                <div className={styles.otpInput}>
                                    <InputWrapper>
                                        <InputWrapper>
                                            <CustomTextField
                                                className={clsx(styles.inputCreateAccount, errors?.otp && styles.error)}
                                                type='text'
                                                mode="wholeNumber"
                                                register={register("otp")}
                                                placeholder='Enter otp'
                                                maxLength={9}
                                                onChange={(e: any) => {
                                                    register("otp").onChange(e)
                                                    setValue('otp', e.target.value);
                                                }}
                                            />
                                        </InputWrapper>
                                    </InputWrapper>
                                </div>
                            )}

                            {
                                showOtpInput &&
                                    (
                                        <>
                                            <button type="button" disabled={!!errors?.otp} onClick={handleVerifyOtp}> SAVE </button>
                                        </>
                                    )
                            }
                        </div>
                    </form>
                </div>
            </Dialog>
        </div>
    )
}

export default TextNotificationNumberPopup