import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { routes } from "../common";

const headerOnlyRoutes = [
    routes.videoLibrary, routes.buyerSettingPage, routes.sellerSettingPage, 
]

export function useLastNonHeaderRoute(excluded: string[] = headerOnlyRoutes) {
  
  const location = useLocation();
  const lastNonHeaderRoute = useRef<string | null>(null);

  useEffect(() => {
    if (!excluded.includes(location.pathname)) {
      lastNonHeaderRoute.current = location.pathname;
    }
  }, [location, excluded]);

  return lastNonHeaderRoute.current;
}