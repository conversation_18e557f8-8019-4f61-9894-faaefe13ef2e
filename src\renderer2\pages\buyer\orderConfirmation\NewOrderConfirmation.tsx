import React, { useState } from 'react'
import styles from './NewOrderConfirmation.module.scss'
import { axios, useCreatePoStore, useGlobalStore, useOrderManagementStore, userRole, useSellerOrderStore } from '@bryzos/giss-ui-library'
import { ReactComponent as ThumbsUpIcon } from '../../../assets/New-images/ThumbsUp.svg';
import { ReactComponent as ThumbsDownIcon } from '../../../assets/New-images/ThumbsDown.svg';
import { ReactComponent as ThumbsUpIconHover } from '../../../assets/New-images/thumbs-up-hover.svg';
import { ReactComponent as ThumbsDownIconHover } from '../../../assets/New-images/thumbs-down-hover.svg';
import { ReactComponent as EmailIcon } from '../../../assets/New-images/EmailIcon.svg';
import { ReactComponent as RightArrowIcon } from '../../../assets/New-images/New-Image-latest/icon-right-arrow.svg';
import { ReactComponent as CloseIcon } from '../../../assets/New-images/close-icon.svg';

import clsx from 'clsx';
import { routes } from 'src/renderer2/common';
import { navigatePage } from 'src/renderer2/helper';
import { useLocation } from 'react-router-dom';
import { useLeftPanelStore } from 'src/renderer2/component/LeftPanel/LeftPanelStore';

interface OrderConfirmationData {
  poNumber: string
  jobNumber: string
  buyerEmail: string
  selectedOptionPayment: string
}

interface NewOrderConfirmationProps {
  orderConfirmationData: OrderConfirmationData
  onClose: () => void
}

const NewOrderConfirmation: React.FC<NewOrderConfirmationProps> = ({
  orderConfirmationData,
  onClose,
}) => {
  const { userData }: any = useGlobalStore();
  const [activeRating, setActiveRating] = useState('');
  const location = useLocation();
  const setSelectedQuote = useCreatePoStore((state: any) => state.setSelectedQuote);
  const setCreatePoData = useCreatePoStore((state: any) => state.setCreatePoData);
  const setClickedCreateNewButton = useLeftPanelStore(state => state.setClickedCreateNewButton);
  const {resetView} = useSellerOrderStore();
  const setOrderManageMentInitialData = useOrderManagementStore.getState().setOrderManageMentInitialData;
  const isBuyer = userData?.data?.type === userRole.buyerUser;


  const handleCreateAnotherPO = () => {
    setSelectedQuote(null);
    setCreatePoData(null);
    setTimeout(() => {
      setClickedCreateNewButton(Math.random());
    }, 0);
    setOrderManageMentInitialData(null)
    navigatePage(location.pathname, { path: routes.createPoPage })
  }

  const handleClaimAnotherSO = () => {
    navigatePage(location.pathname, {path: routes.orderPage}); 
    resetView() 
  }

  if (!orderConfirmationData) {
    return null;
  }

  const handleCheckboxClick = (value: any) => {
    try {
      setActiveRating(value === 5 ? 'thumbs-up' : 'thumbs-down');
      const poRatingPayload = {
        "data": {
          "po_number": orderConfirmationData.poNumber,
          "rating": value.toString()
        }
      }
      axios.post(import.meta.env.VITE_API_SERVICE + '/user/save/purchaseOrderRatings', poRatingPayload)
        .then(res => { })
        .catch(err => console.error(err))

    } catch (err) {
      console.error(err)
    }
  };


  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContainer}>
        {/* Close Button */}
        <button className={styles.closeButton} onClick={onClose}>
          <CloseIcon/>
        </button>

        {/* Header */}
        <div className={styles.header}>
          <h1 className={styles.title}>ORDER CONFIRMED</h1>
          <p className={styles.poNumber}>
            <span>PO#</span> {orderConfirmationData.jobNumber} ({orderConfirmationData.poNumber})
          </p>
        </div>

        {/* Email Confirmation */}
        <div className={styles.emailSection}>
          <div className={styles.emailIcon}><EmailIcon/></div>
          <p className={styles.emailText}>
            <div>WE'VE EMAILED THE ORDER CONFIRMATION TO</div>
            <span className={styles.emailAddress}>{userData?.data?.email_id}</span>
          </p>
        </div>

        {/* Order Status */}
        <div className={styles.statusSection}>
          <p className={styles.statusText}>
            {
              isBuyer  ? (
                "This order is in the queue for suppliers to claim for fulfillment."
              ) : (
                " This order is now ready for your fulfillment per the requirements found on the Purchase Order."
              )
            }
          </p>
          <p className={styles.instructionText}>
            {
              isBuyer ? (
                <>
                  Please email a copy of your PO to{' '}
                  <a href="mailto:<EMAIL>" className={styles.poEmail}>
                    <EMAIL>
                  </a>
                </>
              ) : (
                <>
                  Please email a copy of your SO to{' '}
                  <a href="mailto:<EMAIL>" className={styles.poEmail}>
                    <EMAIL>
                  </a>
                </>
              )
            }
           
          </p>
        </div>

        <div className={styles.accessBox}>
          <p className={styles.accessText}>
            You can now access this order here in Order Management,
            where you are able to:
          </p>

          <div className={styles.actionsGrid}>
            <div className={styles.actionColumn}>
              <div className={styles.actionItem}>
                <span className={styles.arrowIcon}><RightArrowIcon /></span>
                <span>Review order details</span>
              </div>
              {isBuyer && <div className={styles.actionItem}>
                <span className={styles.arrowIcon}><RightArrowIcon /></span>
                <span>Edit this order</span>
              </div>}
              <div className={styles.actionItem}>
                <span className={styles.arrowIcon}><RightArrowIcon /></span>
                {isBuyer ? <span>Chat with the supplier</span> : <span>Chat with the buyer</span>}
              </div>
            </div>
            <div className={styles.actionColumn}>
              <div className={styles.actionItem}>
                <span className={styles.arrowIcon}><RightArrowIcon /></span>
                <span>Cancel this order</span>
              </div>
              {isBuyer && <div className={styles.actionItem}>
                <span className={styles.arrowIcon}><RightArrowIcon /></span>
                <span>Duplicate this order</span>
              </div>}
            </div>
          </div>
        </div>



        {/* Feedback Section */}
        <div className={styles.purchaseRating}>
          <span>
            <p>Was that purchase easy,</p>
            <p> or what? <span className={styles.GiveUsthumbsUp}> Give us a thumbs up!</span> </p>
          </span>
          <div className={styles.purchaseRatingBtn}>
            <button onClick={() => handleCheckboxClick(5)}>
              <span className={clsx(styles.thumbsImages, activeRating === 'thumbs-up' && styles.thumbsImagesActive)}>
                <ThumbsUpIcon className={styles.img1} />
                <ThumbsUpIconHover className={styles.img2} />
              </span>
            </button>
            <button onClick={() => handleCheckboxClick(0)}>
              <span className={clsx(styles.thumbsImages, activeRating === 'thumbs-down' && styles.thumbsImagesActive)}>
                <ThumbsDownIcon className={styles.img1} />
                <ThumbsDownIconHover className={styles.img2} />
              </span>
            </button>
          </div>
        </div>

        {/* Primary CTA */}
        {
          isBuyer  ? (
        <button className={styles.createAnotherButton} onClick={handleCreateAnotherPO}>
          CREATE ANOTHER PO
        </button>

           ) : (
            <button className={styles.createAnotherButton} onClick={handleClaimAnotherSO}>
            CLAIM ANOTHER ORDER
          </button>
           )
        }
      </div>
    </div>
  )
}

export default NewOrderConfirmation
